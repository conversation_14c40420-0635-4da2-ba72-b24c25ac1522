{"cells": [{"cell_type": "code", "execution_count": 36, "id": "d92ca9dc", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from math import log2"]}, {"cell_type": "code", "execution_count": 37, "id": "345375d1", "metadata": {}, "outputs": [], "source": ["def entropy(data):\n", "    target = data.iloc[:, -1]  # The target column is the last column\n", "    counts = target.value_counts()  # Count occurrences of each class\n", "    total = len(target)  # Total number of data points\n", "    ent = 0\n", "    for count in counts:\n", "        prob = count / total  # Probability of each class\n", "        ent += -prob * log2(prob)  # Entropy formula\n", "    return ent"]}, {"cell_type": "code", "execution_count": 38, "id": "64b6f4b7", "metadata": {}, "outputs": [], "source": ["def information_gain(data, feature):\n", "    total_entropy = entropy(data)  # Entropy before the split\n", "    values = data[feature].unique()  # Unique values of the feature\n", "    weighted_entropy = 0\n", "    for value in values:\n", "        subset = data[data[feature] == value]  # Subset of data for each value\n", "        weighted_entropy += (len(subset) / len(data)) * entropy(subset)\n", "    return total_entropy - weighted_entropy  # Gain = Total Entropy - Weighted Entropy"]}, {"cell_type": "code", "execution_count": 39, "id": "781cf88e", "metadata": {}, "outputs": [], "source": ["def id3(data, features):\n", "    # If all target values are the same, return the class\n", "    if len(data.iloc[:, -1].unique()) == 1:\n", "        return data.iloc[0, -1]\n", "\n", "    # If no features are left, return the majority class\n", "    if not features:\n", "        return data.iloc[:, -1].mode()[0]  # Most common class\n", "\n", "    # Find the feature with the highest information gain\n", "    gains = {feature: information_gain(data, feature) for feature in features}\n", "    best_feature = max(gains, key=gains.get)\n", "\n", "    # Create a tree node for the best feature\n", "    tree = {best_feature: {}}\n", "\n", "    # Split the dataset and recursively build the tree for each branch\n", "    for value in data[best_feature].unique():\n", "        subset = data[data[best_feature] == value]\n", "        remaining_features = [f for f in features if f != best_feature]\n", "        tree[best_feature][value] = id3(subset, remaining_features)\n", "    \n", "    return tree\n"]}, {"cell_type": "code", "execution_count": null, "id": "a2b7f05a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Decision Tree:\n", "{'Fatigue': {'Severe': {'Fever': {'High': 'Flu', 'Low': {'Headache': {'Mild': 'Cold', 'None': 'Flu'}}, 'None': 'Flu'}}, 'Mild': {'Fever': {'Low': 'Cold', 'High': {'Cough': {'Wet': 'Flu', 'Dry': 'Cold'}}, 'None': 'Cold', 'Moderate': 'Cold'}}, 'None': 'Healthy', 'Moderate': {'Fever': {'Moderate': 'Flu', 'None': 'Cold', 'High': {'Cough': {'Persistent': 'Flu', 'None': 'Cold'}}}}}}\n"]}], "source": ["# dataset: Medical Diagnosis prediction (30 records)\n", "data = {\n", "    \"Fever\": [\n", "        \"High\", \"Low\", \"None\", \"High\", \"Low\", \"High\", \"None\", \"Moderate\",\n", "        \"High\", \"Low\", \"None\", \"High\", \"Low\", \"None\", \"High\", \"Moderate\",\n", "        \"Low\", \"High\", \"Moderate\", \"None\", \"High\", \"Low\", \"Moderate\", \"High\",\n", "        \"None\", \"Low\", \"High\", \"Moderate\", \"None\", \"High\"\n", "    ],\n", "    \"Cough\": [\n", "        \"Dry\", \"Wet\", \"None\", \"Wet\", \"Dry\", \"Wet\", \"Dry\", \"Persistent\",\n", "        \"None\", \"Wet\", \"Dry\", \"None\", \"Wet\", \"Dry\", \"Wet\", \"Persistent\",\n", "        \"Dry\", \"None\", \"Wet\", \"Persistent\", \"Dry\", \"Wet\", \"None\", \"Persistent\",\n", "        \"Wet\", \"Dry\", \"None\", \"Wet\", \"Persistent\", \"Dry\"\n", "    ],\n", "    \"Fatigue\": [\n", "        \"Severe\", \"Mild\", \"None\", \"Mild\", \"Severe\", \"Severe\", \"Mild\", \"Moderate\",\n", "        \"Severe\", \"Mild\", \"None\", \"Severe\", \"None\", \"Mild\", \"Severe\", \"Moderate\",\n", "        \"None\", \"Severe\", \"Mild\", \"Moderate\", \"Severe\", \"Mild\", \"None\", \"Moderate\",\n", "        \"Mild\", \"Severe\", \"Moderate\", \"None\", \"Severe\", \"Mild\"\n", "    ],\n", "    \"Headache\": [\n", "        \"Severe\", \"Mild\", \"None\", \"Severe\", \"Mild\", \"Severe\", \"Mild\", \"Moderate\",\n", "        \"Severe\", \"Mild\", \"None\", \"Severe\", \"None\", \"Mild\", \"Severe\", \"Moderate\",\n", "        \"None\", \"Mild\", \"Severe\", \"Moderate\", \"Mild\", \"Severe\", \"None\", \"Moderate\",\n", "        \"Severe\", \"None\", \"Mild\", \"Moderate\", \"Severe\", \"Mild\"\n", "    ],\n", "    \"Diagnosis\": [\n", "        \"Flu\", \"Cold\", \"Healthy\", \"Flu\", \"Cold\", \"Flu\", \"Cold\", \"Flu\",\n", "        \"Flu\", \"Cold\", \"Healthy\", \"Flu\", \"Healthy\", \"Cold\", \"Flu\", \"Flu\",\n", "        \"Healthy\", \"Flu\", \"Cold\", \"Cold\", \"Flu\", \"Cold\", \"Healthy\", \"Flu\",\n", "        \"Cold\", \"Flu\", \"Cold\", \"Healthy\", \"Flu\", \"Cold\"\n", "    ]\n", "}\n", "\n", "df = pd.DataFrame(data)  # Convert dictionary to DataFrame\n", "\n", "# Build the decision tree\n", "features = list(df.columns[:-1])  # All columns except the target\n", "decision_tree = id3(df, features)\n", "\n", "# Print the decision tree\n", "print(\"Decision Tree:\")\n", "print(decision_tree)\n"]}, {"cell_type": "code", "execution_count": null, "id": "59bea71f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}