import numpy as np

# Sigmoid activation function
def sigmoid(x):
    return 1 / (1 + np.exp(-x))

# Derivative of sigmoid for backpropagation
def sigmoid_derivative(x):
    return x * (1 - x)

# Input dataset (2 inputs)
X = np.array([[0, 0], [0, 1], [1, 0], [1, 1]])  # OR problem

# Target output (1 output)
y = np.array([[0], [1], [1], [1]])


# Initialize weights and biases randomly
np.random.seed(123)
input_weights = np.random.uniform(-0.5, 0.5, size=(2, 3))  # Weights from input to hidden layer
hidden_weights = np.random.uniform(-0.5, 0.5, size=(3, 1))  # Weights from hidden to output layer
hidden_bias = np.random.uniform(-0.3, 0.3, size=(1, 3))  # Bias for hidden layer
output_bias = np.random.uniform(-0.3, 0.3, size=(1, 1))  # Bias for output layer

# Learning rate
lr = 0.8
print("Initial Input to Hidden Weights:")
print(input_weights)
print("\nInitial Hidden to Output Weights:")
print(hidden_weights)
print("\nInitial Hidden Layer Biases:")
print(hidden_bias)
print("\nInitial output Layer Biases:")
print(output_bias)

# Training the neural network
for epoch in range(10000):  # Train for 10,000 epochs
    # Forward pass
    hidden_layer_input = np.dot(X, input_weights) + hidden_bias  # Input to hidden layer
    hidden_layer_output = sigmoid(hidden_layer_input)  # Output from hidden layer
    output_layer_input = np.dot(hidden_layer_output, hidden_weights) + output_bias  # Input to output layer
    output = sigmoid(output_layer_input)  # Final output

    # Calculate error
    error = y - output

    # Backpropagation
    d_output = error * sigmoid_derivative(output)  # Gradient for output layer
    error_hidden_layer = d_output.dot(hidden_weights.T)  # Error for hidden layer
    d_hidden_layer = error_hidden_layer * sigmoid_derivative(hidden_layer_output)  # Gradient for hidden layer

    # Update weights and biases
    hidden_weights += hidden_layer_output.T.dot(d_output) * lr  # Update weights from hidden to output
    output_bias += np.sum(d_output, axis=0, keepdims=True) * lr  # Update output layer bias
    input_weights += X.T.dot(d_hidden_layer) * lr  # Update weights from input to hidden
    hidden_bias += np.sum(d_hidden_layer, axis=0, keepdims=True) * lr  # Update hidden layer bias

# Display final weights, biases, and output
print("Final Input to Hidden Weights:")
print(input_weights)
print("\nFinal Hidden to Output Weights:")
print(hidden_weights)
print("\nFinal Hidden Layer Biases:")
print(hidden_bias)
print("\nFinal Output Layer Bias:")
print(output_bias)

print("\nFinal Output after Training:")
print(output)

# Test input
test_input = np.array([1, 0])  # Input to test the network

# Forward pass with updated weights and biases
hidden_layer_input = np.dot(test_input, input_weights) + hidden_bias  # Input to hidden layer
hidden_layer_output = sigmoid(hidden_layer_input)  # Output from hidden layer
output_layer_input = np.dot(hidden_layer_output, hidden_weights) + output_bias  # Input to output layer
test_output = sigmoid(output_layer_input)  # Final output

# Display the output
print("Test Input: [1, 0]")
print("Output after training:", test_output)