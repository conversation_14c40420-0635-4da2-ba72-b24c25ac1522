{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b87360fa", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn import datasets\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.decomposition import PCA\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import classification_report, confusion_matrix"]}, {"cell_type": "code", "execution_count": 2, "id": "a2efc95a", "metadata": {}, "outputs": [], "source": ["# Load the breast cancer dataset\n", "cancer = datasets.load_breast_cancer()\n", "X = cancer.data\n", "y = cancer.target"]}, {"cell_type": "code", "execution_count": 3, "id": "bd241e8b", "metadata": {}, "outputs": [], "source": ["# Reduce to 2D using PCA for visualization and training\n", "pca = PCA(n_components=2)\n", "X_2d = pca.fit_transform(X)"]}, {"cell_type": "code", "execution_count": 4, "id": "bc987068", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArAAAAIjCAYAAAD7tihbAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAA8QpJREFUeJzsnQeYE9UXxU/Kwi69916kSxORKr0jvStIF2kCooJUURQsFEEBERDpSBFQ6aAoIFX0j4AgvYP0TpL3/87blyUbEtiFsIW9v+8blk0mk5k3s8md+84916KUUhAEQRAEQRCEWII1undAEARBEARBECKDBLCCIAiCIAhCrEICWEEQBEEQBCFWIQGsIAiCIAiCEKuQAFYQBEEQBEGIVUgAKwiCIAiCIMQqJIAVBEEQBEEQYhUSwAqCIAiCIAixCglgBUEQBEEQhFiFBLCCIAgBolatWujYsSPiMocPH4bFYsG0adMQU6lQoYJenkbeeecdlCxZMrp3QxCeOBLACo8Nv6j4heW5pEmTBhUrVsRPP/2EmMzGjRsxZMgQXLp0KVKvW79+PRo2bIh06dIhXrx4+njr1q2LhQsX4mnG8xzb7XakSJECxYsXR8+ePfH3338/8nZv3LihzwPHNbZeF7/99htWrlyJt99+O+wxHo/nmAUFBSFHjhxo3bo1Dh48eN82rly5gqFDh6Jw4cJIlCgRQkJCULBgQb3NkydP+nzfpk2b6m17vq8QGIYPH47FixcHdJv//fcfPv74Y5QvXx6pU6dGsmTJ8MILL2Du3LkP/WwNDg5GhgwZUL16dYwdOxZXr1697zVvvPEGdu3ahSVLlgR0vwUhpiEBrBAw3nvvPXz77beYPn063nrrLZw7d05npJYtW4aYCgMVBgyRCVQGDx6sg/P//e9/6Ny5MyZMmIC+ffvi2rVraNSoEWbNmoWnmapVq+rzPHXqVH3OixUrhm+++UYHXZ999tkjB7A8DzEpgI3sdcGgpHLlysiVK9d9z/Xo0UOP2aRJk1C7dm0drJQoUSJcUMqAtkiRIhg2bBjy58+PESNG6CCF19rXX3/tM2PIgHfp0qXIli0bZs+eDaXUYxy1EBUB7KZNm/Duu+/qm78BAwbggw8+QIIECdC8eXP92fKgz9Yvv/wS3bt3DwtUCxUqhD///DPcuryprlevHj755JOA7rcgxDiUIDwmU6dO5bem2rp1a7jHL1y4oIKCglTLli0f+Pq7d++q27dvq+jg448/1vt+6NChCK0/f/58vX7jxo3VnTt37nt++fLlaunSpSq2cvPmTeV0Ov0+z2Pv2rXrfY+fP39elSpVSj//ww8/RPp9z507p187ePBgFROI7HVx5swZZbfb1eTJk8M9vm7dOr0dXjeejB07Vj8+fPjwsL+BwoULqwQJEqgNGzbct/3Lly+r/v373/f4lClT9N/Y2rVr9fbWr1+vHhWXy6Vu3LihHheOGfeFnwsxlRdffFEvDyNhwoSqTZs2AX3vgwcPqsOHD9839pUqVVLx48dX165de+hnK1mzZo0KCQlRWbNmve+8fffdd8pisah///03oPsuCDEJycAKTwxOjXEKlFPN3vo4ZgdGjx6NnDlzIn78+GHTz3v37kXjxo11doLTZc8999x9U2EXLlzAm2++qbMPnGZNkiQJatasqafNvPn8889RoEABneFInjy53p47Q8opYmZOSfbs2cOm6biP/hg4cKDetylTpujpYG84tVenTh39/zt37mDQoEF6ij1p0qRImDAhypUrh3Xr1oV7jeeYMEPnHhNm6LZu3Xrfe3CMOG3M6UeOb548eXRGx5MTJ06gXbt2SJs2rd4Wx4D77Il7envOnDk6E5QxY0Y9TszqRZaUKVPq7fBcM6PkJiJjwOPnsRBmPd3ngeeHMMP06quv6ql3XhPMMPHYOBXrCadTmZViNpLHTFkHs8U7duwIt97vv/+OGjVq6P3h8b744ot6+t/No1wXP/zwAxwOB6pUqRKh8apUqZL+eejQIf1zwYIF+vrleSxbtux96/Ma9xxXNzNnztTHyCxtvnz59O8RhePEa3XFihX674LX0sSJE/VzzDxzLDNnzqzHklllZoRdLle4bXA9nhuOJf/e27Rp4zNr7U9zytdyPzzhe4wZM0b/ffN889rg+dq2bVu49WbMmKGvK+43/yaZwTx27Nh97+H+m+J6zz//PDZs2BCh8eE5v379up5dcF8D3F83O3fu1J87PDf8HGL2ffPmzQ/dLq+prFmz3vde9evXx+3bt31KS/xdQ/w8OnLkiB4LT9zX4ffffx+hbQlCbOReZCEIj8nly5dx/vx5PY159uxZHTxyWv3ll1++b11OP9+6dQudOnXSX5D8Atq9ezfKlCmjAykWIjDYmTdvnv5g5xd8gwYN9Gv5Ac9pvSZNmugvgzNnzugvXgYiDISpESNfffWVnrplQEyNJt+PwRADmJYtW2oN6z///KOnXkeNGoVUqVLp17mDKW/279+vg0cGT4kTJ37oeDAQnDx5Mlq0aKELexhgcSqYQe6WLVv0dLEnDKy5DmUJ/EIbOXKk3kcerztY5v4zAOTvHDt++f/77796Gtkd4HA8qKnjNrp166aPh1rk9u3b631iYOIJp6yp4+VNAb9A+f9HIUuWLPocMDjl+/CLPSJjwP3j1GiXLl30OeYxk2effVb/XLVqlR6Dtm3b6uCV1wmDEv5kwMDjJK+99hq+++47fcycgmeA++uvv2LPnj1a5kDWrl2rgw4GPpyutVqt+lpkMMDAhgFOZK8Lt+SAQbx3YOIPnjPC1xD3Tdorr7wS4fGm/IBjzQCLcIy5v+PGjYvwOdy3b59+Ha85nh/eDFHOwfPImyA+zvPK4+vXrx9OnTqlbzwJ/845Vc0x5tgzgF60aJEOYh8HXqfUfvI8dejQQd8Y8NzwXDPQJrzWGbzxRo7rUK7EzxvqShlYMpgmvNZ4DKVLl9bXPa+jl156SX/eMDh/EJyy57Z5TfBvjTAQJrz2+HfIa5xyKf498jOIQfrPP//8SEVUp0+f1j/d11tE4PXSv39/rb32LB7kDQX3lTdmvXr1ivS+CEKsILpTwELsxz3N5b1wOmzatGk+pxeTJEmizp49G+65ypUrq0KFCqlbt26Fm1orXbq0yp07d9hjfN57mpvb5fu99957YY/Vq1dPFShQIGBTxd9//71ed9SoUSoiOByO+6QRFy9eVGnTplXt2rULt+/cbsqUKbXswvv9PCUJ5cuXV4kTJ1ZHjhwJt12Ok5v27dur9OnT62l9T5o3b66SJk0aNt3ont7OkSNHhKeO/UkI3PTs2VOvs2vXrkiNwYMkBL72bfbs2Xr9X375JewxHtuD9o1jxOuoevXq4caL28+ePbuqWrXqI0sIypYtq4oXL37f4+4x5lQ/j/HkyZNaYpEtWzY9xeueGi5atKje/8jwySef6CnkK1eu6N//+ecf/V6LFi2K0Os59cz1KXvxZNiwYXrqnNvz5J133lE2m00dPXpU/7548WL9+pEjR4atw/Ndrly5+yQE/qbsOT3P/XDjlkL06NHjvnXd54zT79yPDz74INzzf/31l5ZxuB+nxCdNmjSqSJEi4a7BSZMm6fd4HAlB/fr1Vbx48cJN0fPc8m+Tf6OR5b///tP7yrHz5EESAje8bnj9eFOtWjWVL1++SO+LIMQWREIgBIzx48frbBkXTmlxWpMZDF+V+Sx28sxoURbA7BgzKszSMZPLhVk0ZuuY/WRGiDBjy8wZcTqdeh1O4TF75DldzCzM8ePHfU7DPwruqfWIZF+JzWYLy4RxWpTHyGwSs0je09qkWbNmWubghhke4p5SZJbpl19+0RlgZsU8cWchGWMyW01HBP7fPY5cOI7Mknu/NzNmnF4NBDwPxF0dHdkx8IXnvjGLzmNhhpl4n29m1/1V6//xxx/6OmL2ndeMe1w4TczpX46t9xR5ROH2PM+dNzxnvN45O8AiLvfUtDujyGsroteVG8oFuC3363Lnzq0zy5GREXAGg9eFJ/Pnz9fXHo/H8/rhtDT/3jhO5Mcff9SSEWbO3fB8u4uMHgVeu7yWfRUzua9xfp7wPPGzwnP/mJ3nGLjlKZQccCaI2WHPjLRb8vCocAyY8eTMEGUtbtKnT6+vLWakIyPD4bG0atVKSy+YRX6UvzlfbgTu8ycITysiIRACBqfa3F/IhFOTRYsW1VO61Np5fonwi9OTAwcO6ICL04JcfMEvI8oL3Bq5L774QmsI+YXixj0lS2grtHr1ar1f1PBVq1ZNf8FQpvAocLqQ+Pqy8AeDlE8//VRLD+7evev3+Il3UOoOiC5evBgukKWtkj8Y5PKLkFPsXPyNoye+9uVRoWSEeAZjkRkDXzDopTaWGlvvfWdA7oaSCwbjnBpmIEcHDNpVuYMMBq/kQVPc3N6DAtEH8SAHAOqAGRQywOMUMafbPbXhvLYiqn0klEVwqpzHx78dN5zC5o2kW8LB8+E+J4Tv73nj6OsccJwoVfEnmXCfA2ovGbS5b1rc8EbyUaG0gkE+p/j9wf3jWDNY9YVbbsP9I97rua3MHhX+jVFm4es4eV75+UQtLnXnEYEB//Lly7V7C508IgvPL/Xe3nCM3EG/IDyNSAArPDGYJWUWlsEmv3Q8P9C9M37uzBd1mN4ZITdueyJa2zDIZVaL+k1+2fG9qHHzzKDxy4QaP9p48QuC2R0GvQwmGBBFlrx58+qff/31V4TWZxaa2R5malgUxC8ZBhAffvhhmAbSEz7ni8hYI7mPn7pjf4GaW1vqJlDZV0JrMR6HOzCK7Bj4gpk2ajD5empmGTDxOFnY43m+uR6DROowmSGjrRULj5ixo57SvS4f99Yfu/EOxiIKb5zcNxq+YEHSgwq8eG0xIGXg8zBtJnEX7VDf6EvjyGudmmEWBnpe69Toehaj+Tr3HCcWhlHb6YtnnnkGkYWBlK/r2PPmM6Jw/7g96rp9/c086jmMDnhu+Jn00UcfRUr/7IYzTLzp8mXdxusxMnpaQYhtSAArPFE4XUw8s0C+cGdEmB15WCU3C3Xc3pieMPPo/YHNQjBOzXNhRTwLdFgAwoIUVjhHJkPBL25mXVjZy6D8YV+U3E8eFwMoz/fx5/X4MNxjxCDRH8yaMfvJwCCiFfGB4ujRo7qApVSpUmEZ2IiOgb/zwC/hNWvW6C963ni4cWdTvWFG8PXXX9cLM4Us3uL5ZgDrLsBhZvJhYxPZzBUDUAaNjwolHywaY2DKa/NBMBBkwR//Bnic3vCmjjICBrDM0Hq6GkTkZoXjxL/Xh40Rg2GeG67r+bfAm0ZvmNX2lWF2Z0k935uuCMy6+8vCch2OAW+SHhRMuwvqeK24XR8IZwE4cxORbKev64B/Y3Sv8HWcnGXgzXREbkKYKafjBW+8H7UJBQvNiK+b/ogeoyDEVkQDKzwx+EXBTBilA8yGPghm5jj9yUpeVjr7mrZzw6yLdzaHuj23RtaNt80S94PV6XyteyqbAS6JqGE9Aylu110d7Q2P1924wZ0d8txXajRpZP4o8IuTVda0w2Kw6In7Pfie1BczmPIV6HqOYyBhwEHJCANnT0uviI4BAwJf58HX64m7Et4N39dTTuC+pjgdTWcFQlkBgx9mJX3dUHmOTWSvCwbtDLYjIwPwhE4ZzNIy2PZ1fVC24h5XVpYzi8oAla/zXnizRh0otcC8eWAg6l4iIp9hJpv7wEDSG46H+7qnRIP/p4OE53nwpePkuDO48xxj2oZ52pcRXrs8175mSNzXAG9CeV1wHe/rgr+7/+4pZ+LfDBuN8ObVDR0OInpeeR34uiYpR+KNrGc2m+4fvLHgDYNbbuQPNrKgQwq1r4/a/IM1A7xZYSDP7XjCvwXOcNB9QRCeViQDKwQMTunxS4ow+8UPc2Y/aIn1sA90d0aCH/78IqclDL98+aXAL1NOlbl9XqmnZWcafoHzA5pT+sw4eeva+CXDwg5+adMPlbpBWgx5Fr4wqCEMDugjyQwws2HuAMYbBgd8PwYanPJl0MZMD780KVNgRsrtM8v9ZOaR1lB8T2ZE+GXKIPphGWl/sDMTx4iZRVr78MuLX6L0IWWREuF0JAMYWvlwHPl+DDBZ8ERNMP//ONBiiplCBgvUWvK88AaCx8QvY07tu4noGDAzyMf4xc6sGrNv1PpyYdBOfStvOqiB5k2C2z/VM8DLlCmTDuDcbVh5rCzgo/6WMDNGSy9mYyln4fXD7fHGh+PFa5R2ZI9yXfDYqGnle7otlyIDt89xYpDJ42UQyeuWj9OyidcUs5i87nitM4jie/qCNlHcb2qGe/fuHel9oVSDtl48d5R/cCxYdMbrnhl1Xm+c6eB4cB/5983HeP54DN43EoRyH14bzBTSJoufD7wOeB48C56YVeZUOq9zfna4ZSK00eJz1NMzGH7//fd1pprvS3kK/555TVA+wvGnFIljx/Voo8UMLP92uQ5t0yKqgeWx85xy33kzxL83/l1xuyxW5d8is+A897z55s0Sr9UHQfs4ZsYpO2HxoHfRHT/TvPfP/dnKGwZ+JjJ45fvzs4fnirNJnnCf3TZngvDUEt02CMLTaaMVHBys7Wu+/PLLcJZFbsso2hT5grY0rVu3VunSpdMdhjJmzKjq1KmjO8t42mj16dNHW0XRRqhMmTJq06ZN91n1TJw4UVva0J6KFls5c+ZUffv21V2NvG2D+D5WqzXC1knsgkObLlrf0LonderUqm7dutr6yg2Pm52WaBPE96fVzbJly+6zDnrQmPiylvrf//6nGjRooJIlS6bHOU+ePGrgwIH3dYaipVTmzJn1OHI8aVNGC6GHdYl6EJ7nmOPFfeBx0T5r9+7d960f0TEgGzdu1FZUtCfyPO7jx4+HHS8tg5o0aaItizzXoU0Szy27WdHKiPZH/P8XX3xx3z7t3LlTNWzYMOy64H40bdpUn9PHuS5eeuklPcaeRHaMaTE2aNAgbSfHrlw8vwULFlT9+vVTp06d0tZQ3G9vuyVvaAvmy1rJEx537dq1fT539epV/Z65cuXS5yNVqlTazo7WXZ4d6Gj/9Morr2hbPJ4b/p/j66sT14wZM7RlG7fHz4YVK1b4vA5oxcW/hbx58+p1+bdVs2ZNtX379nDrLViwQNuX8Vxz4fq85vft2xduPV4DHA+e6+eee05br0W0E9fevXv1Zwg/Z3hMnpZaO3bs0JZsiRIl0ueqYsWK+hp+VNtB9+I5bt7rcjz4t0zLtzFjxoRZqHnTrFkzPTaC8DRj4T/RHUQLgiDEdpglpAyGmTJ/FfKC8KRhQwRmipmBlwys8DQjAawgCEKAoDyBUgZ2gROE6ICSDkoMKFUQhKcZCWAFQRAEQRCEWIW4EAiCIAiCIAixCglgBUEQBEEQ4hC//PKLdhKhuwb9jhcvXvzQ16xfv1474LCdO5tn0JIuOpEAVhAEQRAEIQ5x/fp1bTlI+8qIQAs6WvfRzo6WjWzAQT90X37RUYVoYAVBEARBEOIoFotFeyjTU9kf7BZHv3HPBjn0yGajD3qgRwexppEBO7yw7R4N1GkTwrQ3TbYHDBgQ1u6PsThbVLICmINKk212ifG0tKGJe/fu3bVhOY3N2fklIm1B3dBUmx1uaJwd2XaTgiAIgiBED4wR2PSE8QO//6OaW7duhesKF+hjs3jFJJzq5xII2FDIu700G5MwExttqFjCBx98oA28aYJOQ3Eag9NAmmbObj766CNtpr148WK1a9cubSxOA+ubN2+GrVOjRg1tcL5582a1YcMGbdTdokWLCO/HsWPHHmhCLYssssgiiyyyxNyF3+NRDeOQdE/wmBIlSnTfY95NcPzBdRctWvTAdXLnzq2b0njyww8/6NfeuHFDRQexJgO7ceNGbcrsbp+YLVs2zJ49O8zrjueA/dGZkXWbN0+fPl23EKU4maluthJlqpvtJdknm7BvN3t6sz8678oehrsF6bFjxx7YHpVtL9nyku1M2dJQiDpk7KMPGfvoRcY/+pCxj/ljz7bFmTNnDvsej0qYeT3N2AHAwxurR44rADJfu3ZfXBKo7GtMJdYEsOwPPWnSJN2Hnb3S2X/9119/1T2q3QJjSgs8U9xJkybVfauZ+mYAy5/JkiULC14J1+dUwu+//677tXvD3tZc3HD6wd27nYs/2Bs7QYIEeh35MItaZOyjDxn76EXGP/qQsY/5Y89Al0Sn/C+JxaKXgKKUXhi8Piix9jikS5cOZ86cCfcYf+f7PSgWepLYY1N3Ed495c2bFzabTWtiP/jgA7Rq1Uo/z+CVMOPqCX93P8efadKkue/CT5EiRdg63nz44YcYOnTofY/zbo9/MA9j1apVkThKIZDI2EcfMvbRi4x/9CFjH3PH/saNG4h2qL19EgGs04knSalSpfDjjz/eN958PLqINQHsvHnzMHPmTMyaNQsFChQIs3HgtH+bNm2e2Pv269cPvXv3vm8KglMVD5MQ8ORWrVpV7sajGBn76EPGPnqR8Y8+ZOxj/tjz+1sI5dq1azhw4ID5LXQWm3EVE3pZsmTRsc+JEye0FJO89tprGDduHN566y20a9dOtytmXEZngugi1gSwffv21VlYSgFIoUKFcOTIEZ0hZQDL9LY7pZ0+ffqw1/H3IkWK6P9znbNnz4bbrsPh0M4E7td746+Kj38kEfmQiuh6QuCRsY8+ZOyjFxn/6EPGPuaOfYw4LzEkA7tt2zbt6erGnahjPMUGBadOncLRo0fDns+ePbsOVnv16qWdmzJlyoTJkydrJ4LoItYEsEz9e9teUEpAWyv34DIIXbNmTVjAyrstalu7dOmif2eqm/Za27dvR/HixfVjvIvgNqiVFQRBEARBeNqpUKGCLn73h68uW3zNzp07EVOINQEsW55R88rUNiUEHEQWcDGV7RZlU1Lw/vvva99XBrQDBw7UEgO3OW++fPlQo0YNdOzYERMmTNDTDt26ddNZ3Yg4EAiCIAiCIMS4DGwcJNYEsLS7YkD6+uuvaxkAA87OnTtj0KBBYetQm8H2aJ06ddKZ1rJly2rbrODg4LB1qKNl0Fq5cuWwRgZjx46NpqMSBEEQBEEQntoAlr5t9Hnl4g9mYd977z29+IMCZRaCCYIgCIIgRCmSgQ0YUd9LTRAEQRAEQRDiQgZWEARBEAQhViMZ2IAhAawgCIIgCEJUIAFswBAJgSAIgiAIghCrkAysIAiCIAhCVCAZ2IAhGVhBEAQhoNy6dUt3Svzvv/+ie1cEQXhKkQBWEARBCAgMWLt3746UKdMgW7ZsSJUqFSpUqIx169ZF964JQszJwD6JJQ4SN49aEARBCHjw+sILZfHllzNx40Z3ACsATMGvv15D5cpVMG/evOjeRUEQniJEAysIgiA8NuyKeOjQWTidvwPIHfa409kaFksrtGvXEbVq1UKiRImidT8FIVqh/jXQGVOXC3ERycAKgiAIj8WNGzcwdep0OJ1dwwWvodig1AjcuHENc+fOjaY9FAThaUMysIIgCMJjcfLkSdy8eQ1ART9rZIXdnhN79uyJ4j0ThBhGHNasBhoJYAVBEITH4p4s4JSfNe7A5Tov8gFBkAA2YMgoCoIgCI9FunTpULJkaVitEyjI87HGPDidF9G4ceNo2DtBEJ5GJIAVBEEQHpshQwZCqV8BtPfIxN4FMAs2WxfUr98QBQsWjOa9FIRoRmy0AkbcPGpBEAQhoNSoUQPTpk1DcPA8WCxZEBRUEHZ7BgCtULt2FcyYMT26d1EQhKcI0cAKgiAIAaF169aoV68eZs6ciX379iFJkiRaNlC4cOHo3jVBiBnE4YxpoJEAVhAEQQgYSZMmxeuvvx7duyEIwlOOBLCCIAiCIAhRgWRgA4aMoiAIgiAIghCrkAysIAiCIAhCVCAZ2IAhAawgCIIgCEJUIAFswJBRFARBEARBEGIVkoEVBEEQBEGICiQDGzBkFAVBEARBEIRYhWRgBUEQBEEQogKLJfAZWKUQF5EMrCAIgiAIghCrkAysIAiCIAhCbNXAKsnACoIgCIIgCEKMRzKwgiAIgiAIUYFkYAOGBLCCIAiCIAhRgQSwAUMkBIIgCIIgCEKsQjKwgiAIgiAIUYFkYAOGZGAFQRAEQRCEWIVkYAVBEARBEKICycAGDMnACoIgCIIgCLEKycAKgiAIgiBEBZKBDRiSgRUEQRAEQRBiFZKBFQRBEARBiAokAxswJIAVBEEQBEGICiSADRgiIRAEQRAEQRBiFZKBFQRBEARBiAoslsBnYF0uxEUkAysIgiAIgiDEKiQDKwiCIAiCEFs1sNa4mYuMm0ctCIIgCIIgxFokAysIgiAIghAVSAY2YMTNoxYEQRAEQRBiLZKBFQRBEARBiAokAxswJIAVBEEQBEGICiSADRix6qhPnDiBl19+GSlTpkRISAgKFSqEbdu2hT2vlMKgQYOQPn16/XyVKlWwf//+cNu4cOECWrVqhSRJkiBZsmRo3749rl27Fg1HIwiCIAiCIDzVAezFixdRpkwZBAUF4aeffsLff/+NTz/9FMmTJw9bZ+TIkRg7diwmTJiA33//HQkTJkT16tVx69atsHUYvO7evRurVq3CsmXL8Msvv6BTp07RdFSCIAiCIMS5DGyglzhIrJEQjBgxApkzZ8bUqVPDHsuePXu47Ovo0aMxYMAA1KtXTz82ffp0pE2bFosXL0bz5s2xZ88eLF++HFu3bsVzzz2n1/n8889Rq1YtfPLJJ8iQIUM0HJkgCIIgCILwVAawS5Ys0dnUJk2a4Oeff0bGjBnx+uuvo2PHjvr5Q4cO4fTp01o24CZp0qQoWbIkNm3apANY/qRswB28Eq5vtVp1xrZBgwb3ve/t27f14ubKlSv65927d/XiD/dzD1pHeDLI2EcfMvbRi4x/9CFjH/PHPkacG9HAxr0A9uDBg/jyyy/Ru3dv9O/fX2dRe/TogXjx4qFNmzY6eCXMuHrC393P8WeaNGnCPW+325EiRYqwdbz58MMPMXTo0PseX7lyJRIkSPDQ/aZUQYgeZOyjDxn76EXGP/qQsY+5Y3/jxo0o2xfhyRNrAliXy6Uzp8OHD9e/Fy1aFP/73/+03pUB7JOiX79+Omj2zMBSylCtWjVdCPagOz3+MVWtWlXrdoWoQ8Y++pCxj15k/KMPGfuYP/buGdRoRTKwcS+ApbNA/vz5wz2WL18+LFiwQP8/Xbp0+ueZM2f0um74e5EiRcLWOXv2bLhtOBwO7Uzgfr038ePH14s3/COJyIdURNcTAo+MffQhYx+9yPhHHzL2MXfs5bw8XcSasJ0OBPv27Qv32D///IOsWbOGFXQxCF2zZk24uy1qW0uVKqV/589Lly5h+/btYeusXbtWZ3eplRUEQRAEQXhiiAtB3MvA9urVC6VLl9YSgqZNm2LLli2YNGmSXojFYsEbb7yB999/H7lz59YB7cCBA7WzQP369cMytjVq1NCFX5QecNqhW7duusBLHAgEQRAEQXiiWCyBDzgtFsRFYk0AW6JECSxatEhrUt977z0doNI2i76ubt566y1cv35d+7oy01q2bFltmxUcHBy2zsyZM3XQWrlyZe0+0KhRI+0dKwiCIAiCIMQOYk0AS+rUqaMXfzALy+CWiz/oODBr1qwntIeCIAiCIAh+kCKugBE3j1oQBEEQBEGItcSqDKwgCIIgCEKsRTKwASNuHrUgCIIgCIIQa5EMrCAIgiAIQlQgGdiAETePWhAEQRAEQYi1SAZWEARBEAQhKpAMbMCIm0ctCIIgCIIgxFokAysIgiAIghAVSAY2YEgAKwiCIAiCEBVIABsw4uZRC4IgCIIgCLEWycAKgiAIgiBEBZKBDRhx86gFQRAEQRCEWItkYAVBEARBEKICycAGjLh51IIgCIIgCEKsRTKwgiAIgiAIUYHFEviMqcWCuIhkYAVBEARBEIRYhWRgBUEQBEEQogLRwAYMCWAFQRAEQRCiAglgA0bcPGpBEARBEAQh1iIBrCAIgiAIQlRmYAO9PALjx49HtmzZEBwcjJIlS2LLli0PXH/06NHIkycPQkJCkDlzZvTq1Qu3bt1CdCEBrCAIgiAIQhxi7ty56N27NwYPHowdO3agcOHCqF69Os6ePetz/VmzZuGdd97R6+/Zswdff/213kb//v0RXUgAKwiCIAiCEIcysJ999hk6duyItm3bIn/+/JgwYQISJEiAKVOm+Fx/48aNKFOmDFq2bKmzttWqVUOLFi0emrV9kkgAKwiCIAiCEMu5cuVKuOX27ds+17tz5w62b9+OKlWqhD1mtVr175s2bfL5mtKlS+vXuAPWgwcP4scff0StWrUQXYgLgSAIgiAIQix3IcicOXO4hzndP2TIkPtWP3/+PJxOJ9KmTRvucf6+d+9en2/BzCtfV7ZsWSil4HA48Nprr0WrhEACWEEQBEEQhFjOsWPHkCRJkrDf48ePH7Btr1+/HsOHD8cXX3yhC74OHDiAnj17YtiwYRg4cCCiAwlgBUEQBEEQYnkGNkmSJOECWH+kSpUKNpsNZ86cCfc4f0+XLp3P1zBIfeWVV9ChQwf9e6FChXD9+nV06tQJ7777rpYgRDWigRUEQRAEQYgjRVzx4sVD8eLFsWbNmrDHXC6X/r1UqVI+X3Pjxo37glQGwYSSguhAMrCCIAiCIAhxiN69e6NNmzZ47rnn8Pzzz2uPV2ZU6UpAWrdujYwZM+LDDz/Uv9etW1c7FxQtWjRMQsCsLB93B7JRjQSwgiAIgiAIcaiVbLNmzXDu3DkMGjQIp0+fRpEiRbB8+fKwwq6jR4+Gy7gOGDAAFotF/zxx4gRSp06tg9cPPvgA0YUEsIIgCIIgCHGMbt266cVf0ZYndrtduxpwiSlIACsIgiAIghAVWCyBz8BaLIiLSBGXIAiCIAiCEKuQDKwgCIIgCEIc0sA+DcTNoxYEQRAEQRBiLZKBFQRBEARBiAokAxswJIAVBEEQBEGICiSADRhx86gFQRAEQRCEWItkYAVBEARBEKICycAGjLh51IIgCIIgCEKsRTKwgiAIgiAIUYFkYANG3DxqQRAEQRAEIdYiGVhBEARBEISoQDKwASNuHrUgCIIgCIIQa5EMrCAIgiAIQlQgGdiAIQGsIAiCIAhCVCABbMCIm0ctCIIgCIIgxFokAysIgiAIghAVWCyBz5haLIiLSAZWEARBEARBiFVIBlYQBEEQBCEqEA1swIi1R/3RRx/BYrHgjTfeCHvs1q1b6Nq1K1KmTIlEiRKhUaNGOHPmTLjXHT16FLVr10aCBAmQJk0a9O3bFw6HIxqOQBAEQRAEQYgzAezWrVsxceJEPPvss+Ee79WrF5YuXYr58+fj559/xsmTJ9GwYcOw551Opw5e79y5g40bN+Kbb77BtGnTMGjQoGg4CkEQBEEQ4mQGNtBLHCTWHfW1a9fQqlUrfPXVV0iePHnY45cvX8bXX3+Nzz77DJUqVULx4sUxdepUHahu3rxZr7Ny5Ur8/fffmDFjBooUKYKaNWti2LBhGD9+vA5qBUEQBEEQhJhPrNPAUiLALGqVKlXw/vvvhz2+fft23L17Vz/uJm/evMiSJQs2bdqEF154Qf8sVKgQ0qZNG7ZO9erV0aVLF+zevRtFixa97/1u376tFzdXrlzRP/leXPzhfu5B6whPBhn76EPGPnqR8Y8+ZOxj/tjHiHMjGti4GcDOmTMHO3bs0BICb06fPo148eIhWbJk4R5nsMrn3Ot4Bq/u593P+eLDDz/E0KFD73uc2VzqaB/GqlWrHrqO8GSQsY8+ZOyjFxn/6EPGPuaO/Y0bNxDtSAAb9wLYY8eOoWfPnvoCDQ4OjrL37devH3r37h0uA5s5c2ZUq1YNSZIkeeCdHve1atWqCAoKiqK9FYiMffQhYx+9yPhHHzL2MX/s3TOowtNBrAlgKRE4e/YsihUrFq4o65dffsG4ceOwYsUKrWO9dOlSuCwsXQjSpUun/8+fW7ZsCbddt0uBex1v4sePrxdv+EcSkQ+piK4nBB4Z++hDxj56kfGPPmTsY+7Yx4jzIhnYgBFrjrpy5cr466+/8Mcff4Qtzz33nC7ocv+fF+eaNWvCXrNv3z5tm1WqVCn9O39yGwyE3fCujZnU/PnzR8txCYIgCIIgCE9pBjZx4sQoWLBguMcSJkyoPV/dj7dv315P96dIkUIHpd27d9dBKwu4CKf9Gai+8sorGDlypNa9DhgwQBeG+cqyCoIgCIIgBAzJwMa9ADYijBo1ClarVTcwoHMAHQa++OKLsOdtNhuWLVumXQcY2DIAbtOmDd57771o3W9BEARBEAQhjgSw69evD/c7i7vo6crFH1mzZsWPP/4YBXsnCIIgCILggWRgA0bcPGpBEARBEAQh1hKrM7CCIAiCIAixBsnABgwJYAVBEARBEKICiyXwAafFgrhI3AzbBUEQBEEQhFiLZGAFQRAEQRCiApEQBIy4edSCIAiCIAhCrEUysIIgCIIgCFGBZGADRtw8akEQBEEQBCHWIhlYQRAEQRCEqEAysAEjbh61IAjCI3D06FG8++67KFbseTz7bHF07doVu3fvju7dEgRBiHNIBlYQBCECrFixAvXrN8Tdu3Y4nfUBxMOePQvx5ZcTMGHCl+jUqVN076IgCDEdycAGDAlgBUEQHsLJkyd18Hr7diUoNQtAYv24w3EHQC+89tprKFy4MEqWLBnduyoIQkxGAtiAETePWhAEIRJ89dVXuHvXAqVmhAWvocQD8DlsthwYM2ZsNO6hIAhC3EIysIIgCA9h1aq1cDprAUjq41krHI7mWLny62jYM0EQYhWSgQ0YcfOoBUEQIoFSCoDtAWvYzDqCIAhCVCAZWEEQ4jTnzp3DggUL9M8sWbKgUaNGSJQoUbh1XnyxLH7/fTyczmsAwj8HKNjt81GhQtko3W9BEGIhkoENGHHzqAVBiPO4XC4MHDgQGTJkwuuvd8d7743Hq6+2Rdq0GbTm1ZPOnTvDar0Fi4VOA3c8twJgAByOPejZs0eUH4MgCEJcRQJYQRDiJMOGDcP7778Ph+NtKHUaDsdpAIdx40ZTbYk1axbdBkLJmjUrZs2aCZvtO9jt2QD0BPAW7Pa8AIZj5MiRKF++fLQejyAIsSgDG+glDhI3j1oQhDjN5cuX8dFHH+sgFHgPQErzTBZ6DgBogHffHayztG4aN26MXbv+QIcO9ZA164/ImHE+Gjd+Dr/99hv69u0bbcciCIIQFxENrCAIcY5ly5bh1q3rJpPqjQXAGzh8+EVs374dJUqUCHsmf/78+PLLL6N0XwVBeIqwWAKfMbXwMyvuIRlYQRDiHBcvXoTFEgQgg581soatJwiCIMQ8JAMrCEKkcTqdeho+YcKEiB8/PmIbOXPmhFJ3AWwHUNzHGpv0vzly5IjyfRME4SlGXAgCRtw8akEQHokLFy5ovWfKlGmRMmVKJEyYCE2aNMUff/yB2ES1atWQIUMWWCwD2RDW69mrsNk+QLlyFZArV65o2kNBEJ5KpIgrYMTNoxYEIdKcP38eJUuWwahRk3D5cmsA38Hp/AiLFv2BF14ojXXr1iG2YLPZMHnyBFitq2C1lgMwF8BOAJNhsz2P4OCjGDduTHTvpiAIguAHCWAFQYgQb731Ng4dOg+ncxuAzwA0AtAHTucu3L1bGi1avIK7dzktHzuoWbMmVq9ehZIlqaRqDqCY9nmtVi0nNm/+Dc8++2x076IgCE8bkoENGKKBFQThoVDvOnPmLDidgwDk9no2BC7XJzhzpihWrFgBayz6MK1QoQI2btyAw4cP605cmTJlQvr06aN7twRBEISHIAGsIAgP5d9//8WdO7cAVPWzRhHY7Wnw999/o2DBgohtZMuWTS+CIAhPFCniChhx86gFQYgUCRIkMP8772eNW3C5riEkJCQK90oQBEGIq0gAKwjCQ8mTJw9y5swDi4VdqnwxEy7XDdSqVSuK90wQBCEWIRrYgBE3j1oQhEhhsVgwePC7UGohgH4ArphnnNqNwGrticaNm2p/VUEQBEF40ogGVhCECPHKK6/g5MmT6N//XVgs42CxFAJwDA7HcdSoUQfTpk2J7l0UBEGI2YgGNmBIACsIQoR5++230apVK0ybNk0XdiVLVhItWrRAiRIldJY2NtloCYIgRDkSwAYMCWAFQYgUtJoaMGBAdO+GIAiCEIeRAFYQBEEQBCEqkAxswIibRy0IgiAIgiDEWiQDKwiCIAiCEBVIBjZgxM2jFgRBEARBEGItkoEVBEEQBEGICiyWwGdMLRbERSQDKwiCIAiCIMQqJAMrCIIgCIIQFYgGNmBIACsIgiAIghAVSAAbMCJ11Ddv3sSvv/6Kv//++77nbt26henTpwduzwRBiBDHjx/Hl19+iU8++QQ//vgjnE5ndO+SIAiCIMSMDOw///yDatWq4ejRo7plZNmyZTFnzhykT59eP3/58mW0bdsWrVu3fpL7KwiC4fbt2+jatRumTp0KpSywWhPA6byCTJmyYebMb1C+fPno3kVBEATBE8nABgxrZHqgFyxYEGfPnsW+ffuQOHFilClTRge0giBEPW3atMXUqd/C5foUSl2A03kZwBacPJkN1arVwB9//BHduygIgiAI0RvAbty4ER9++CFSpUqFXLlyYenSpahevTrKlSuHgwcPPpm9EwTBJ3/99Rfmzp0Nl2sCgJ4AEptnSsDl+hEORxYMG/Z+NO+lIAiC4DMDG+glDmKNjP7Vbr+nOKCMgLq7unXr4sUXX9QSA0EQooZZs2bBbk8NoJWPZ0PgdHbB4sWLcf369WjYO0EQBEGIIRrYvHnzYtu2bciXL1+4x8eNG6d/vvTSS4HfO0EQfHLhwgUAmQEE+VkjB1wuJ65cuYKECRNG8d4JgiAIPhENbMCI8FE3aNAAs2fP9vkcg9gWLVpAKRW4PRMEwS/ZsmWDy7WX5ZN+1tiCBAkSI2XKlBHeJgsxDx8+LFlbQRAE4ekJYPv166ctevzxxRdfwOVy4UlB/W2JEiV08ViaNGlQv359XUzmbeXVtWtX/aWdKFEiNGrUCGfOnAm3DovOateujQQJEujt9O3bFw6H44nttyA8CpTkdOvWDRkyZEWKFGlRpUp1fP/992E3iW3atIHFcgfARz5efQx2+wS8+uoriBcv3kPfa8eOHahbtx5SpEiB7NmzI0WKVNpRRAo0BUEQAoxoYANGrDnqn3/+WQenmzdvxqpVq3D37l1t6+WZLerVq5cuLps/f75e/+TJk2jYsGHY8/THZPB6584dXZT2zTffYNq0aRg0aFA0HZUg3M+KFSvw7LNFMHHidzh1qjEuXuyG9euv6Ju2Ll1e10FsunTp8NZbfU0A2xzAOgD0Zx4Lu70U0qZNiIEDBz70vTZs2IDSpcvip5/2w+X6HMBK3LkzCDNmrETx4iWlQFMQBCGQSAAb9zpxLV++PNzvDDyZQd2+fbv2u+T059dff62LWypVqqTXoT8mNbsMel944QWsXLlSN2FYvXo10qZNiyJFimDYsGHaImzIkCERylYJwpPk0qVLaNSoCe7cqQilvtMFWcTpZDA6BRMntseVK5fx66+bcezYIf2c3b4EDsdc/X+bzY4GDRpi1KjPdJD7IDhj8sorbXH3Lp0Lloe9F1AVDkd7XLpUCt2798QPPywN9zr+Df355596FqNixYp6VkQQBEEQopJYE8B6w4CVcNqTMJBlVrZKlSrhCs+yZMmCTZs26QCWPwsVKqSDVze0AuvSpQt2796NokWL+jSL5+KGRTGE78XFH+7nHrSO8GSIzWPPbnZUCQQHTzR/np7H8AqAhVi8eBmAJggJGQWA8pe5CApapnXqI0eO1Dd2ETn+devW4ezZk4gf/xsf75UcQH+sX99D62IzZsyI/fv3o1u3nti8+bewtRIkSITXX38N/fv3h81mi9Vj/zQg4x99yNjH/LGPEedGirgChkXFwsorZo7oesBsFVvbEmZeqdvzDDbJ888/r7NEI0aMQKdOnXDkyBE9Revmxo0bukqb+t6aNWve917MzA4dOvS+x/l+zEAJgiAIghDz4fd9y5YtdQIsSZIkUfreTH4lTZoUl9esQZIAO8NcuX4dSStXjvRxjR8/Hh9//DFOnz6NwoUL4/PPP9cxkz8Yc7377rtYuHChdsLJmjUrRo8ejVq1aiFWZGB/+eUXlC5dOpwnLGEhFHWlUdG+klrY//3vf2HB65OExWu9e/cOdxFmzpxZ628fdKHwTo9a3apVqyIoyJ/VkfAkiKqx5zVPXXX8+PEDts0OHTpg4cLDcDpX+1njKwDUvv5HwYDH4y7Y7UXRuHEpTJzI5gb+b/7o4cxlxowZuhUtQJ1r6ExGeDYBqIE1a9Zg5syZmD79BzgcfMzb2WAygD7YsmULcuTIIdd9NCKfO9GHjH3MH3v3DGq0YrEEPmNqsUT6JXPnztWxzYQJE1CyZEkdiHJGmsXx7lk8T1g7xPHlc999952elWNCMFmyZIguIh3AMpt56tSp+w6QkT+f4xf6k4SV2cuWLdOBdKZMmcIep96PA8w7BM8BpQuBWwvIn/yS9cTtUuBPL8jgxFeAwj+SiHxIRXQ9IfA8qbHntTdixMdYvvxHHRDmyPEMevR4Ha+//vpjv1+FChUwfXo7AEcA5PJ6lpMlnO5/AUCwj1eXxs6df963D/xwnzhxIsaNm4B9+3YjfvwQNGhQH+3bt8Pduw44HAxA3/WxvYnImDENihUrhsqVq+LmzTf5l+Jjvfaw2wfrgJiaciLXffQi4x99yNjH3LGX83KPzz77DB07dtQz14SB7A8//IApU6bgnXfegTd8nFlXJird40g7x+gk0rcBVBwwe+PNf//990QN0/m+DF4XLVqEtWvXarsfT4oXL64HldkiN7yToBVQqVKl9O/8yRacZ8+eDVuHd23MpObPn/+J7bvw9PDtt9+iQoWKWLHiGFyuz3RAeehQMfTq1Qf16jV4bI1Vs2bNkCZNBthsdBY46fEMtztA+7sy2+kLi+U0kiRJFO4x3tTVqVMPPXq8gX/+YROSibh9+118990W1KxZGzVr1oDFMgQAHQhumlddBPA2hTIYMmSA7sJ38ybdPgr62et4UOoZ7fohxDx4TS5YsEB/WdF+jb7d7hoCQRCeHheCK1euhFu8JZWe3wusG/KsGbJarfp31gr5YsmSJTqG4gw464gKFiyI4cOHP/GkZUAysG47Kgavr776arisJA+AVcmUFjwpOGjUndILk1XP1GwQakpCQkL0z/bt2+uUOAu7GJR2795dDzgLuAin/RmovvLKK7rYhdsYMGCA3nYgp4GFpxNm69u37wCl2sDp/CpsCl+p1gBexfLltXV75R49ejzye1BXvWLFD6hSpQYuXMgGpajLTg67fSUcjlOwWIKhVOgNWXjoSLAcTZuOCffomDFjtOuGUj9pdwE3DkdfWCzN9A3fK6+0wvTpPWCzDYTVmhlO5wFYrU68//5HWtJAqURISELcvPk/AI19vPcdWCz/IH36Mo983MKT4d9//0W1arVw8OA/sNsLQalE+PbbWXjnnf6YO3e2thUUBOHpIHNmdme8x+DBg3Udjzfnz5/XcZtnQTvh73v3skHO/dBSkcnDVq1a6ZqhAwcO6FlH3iDzfWJ0AMsA0Z0JZQDJoNEN7acYJPIO/0nBwMA9xeoJrbIYUJNRo0bpuwg2MOCdB/UcbLDghlXSlB/QdYCBLTPGzEi89957T2y/hacHTqE4nQxaP/XSn5LqABrh888fL4AltHfbv3+P9iletGgJbt06j+eea6B9YBs3bobr12vB6eS0fwEjK2BW9mUtDeCNGwNOatT5tzp2LBuMtAwXvN7Lmo7DzZtZUbLk8xgw4F3daY8fbFmzvoqXX3457MON22rd+mV8/fVEOBzUzKby2tZkOBzn9N+SEHNg5rxSpWo4eZIf8zvgcLhdVk7ixo0uaNCgEbZv36qdWQRBiP0uBMeOHQtXmxPIxBzlcpSOTpo0ScdSnPU+ceKELgKL8QEsA0W35uHNN9+M8v7qETFLCA4O1lV1XPzBqrkHdRQTBH/s2rXL6E9pMXU/zJYeODBPT888rqdw8uTJ8cYbb+jFk9WrV+Cllxri9OmCsFhyQ6lbuvMW9+nOnZxo3bo1xo37EitW/Kj/Zo4fPwxgpJ93yQibrZieSuKd9IMaerCYcf78Rbh8uRycTupcq5lCsq9gsYxAhw6d9OxGjLCpEcKKNI4eZYHeHpoKejyTAUrN07IP6uDcn+2CIMTuADZJkiQRciFIlSqVDkK9O5V61gx5kz59ei3T5Ovc0GefM9mB+M57FCI9ioy0ozp4FYSYAG+QrFbqQ/1xEVar7T6HjkDCdspHjvyrJT1KHTC6VDYaOAeX6w/21sL27XvRpk07jw8Uf5W3vCm8HKG7dN74bdz4C0qWTK09aAHOyORAggTj0L9/P3z55b2ZDiFmQLmV1VrOK3h1Ex8ORxssXPh9NOyZIAjRSbx48XQG1bNmiBlW/u6uGfKmTJkyWjbA9TxbnjOwja4mUJEOYBmhU0OaIUMG/UXNaNxzEYSnlbp168LhYJC43cezTthsU1G7dh0tY3mSULu0ejVbx9LejbMJdTwkDWXhdH6KJUsWabeQcuUq6P0KDVa92QSH4x99XBEhT548+O23X7SF3Zw5c3SAdPr0Cbz//vvytx8DuXHjJlwub8szT1Lh1i134Z4gCHGplWzv3r3x1Vdfaananj17tLTy+vXrYa4EnM3jzJsbPk8Xgp49e+rAlY4FLOJiDVF0EelUEfWmrOxnn3VG3r4cCQThaaRevXrInTsfDh1qDIdjPoDnzDOcSu8Nl2s33nrryWcit23bhitXLprOXL5oDouloy7eeuedvqZQpxeA9wG4XQp2wm5viWeeeVZrxSNDgQIF9OLm6tWrurJ94sSvcf78GcycOUN/8PGDzruoQIg6Chd+FmvWfA2nk0HqvZoFN1brchQoIPpXXx7JgvC006xZM5w7d05LxygDYO3F8uXLw2ofGOd5JmP4Wc4mUL169cKzzz6rfWD5Gf/223SsiSUBLJsHbNiwQR+sIMQlOOOwevVyVK1aE//8UwJ2+7NwuaiH3QwmIKdN+xZly5Z94vtxz7bElxcs4XSOVRdzseiRBVkzZoyFxTIFSpWFzXYOTuc25MiRHz/9tPSxMsYXL15EuXIVsWfPPrhczRESUlI/PnHiHEyZMh0bNqwLF+wKUQc7D37yySfGfo0/PQOzxXC5lqN7968R1wvdePP1xReTcPjwAe220axZE/Tt21esFYWnvpVst27d9OKL9evX3/cY5QWbN29GTCHSASyj8FjYfVYQAkKWLFmwe/cuLF26VC+3bt1C4cJD9bSLr+4lTwLe/QYFxcfdu4uNX6s3LOC6q9smpkuXEVevXobdnhFO5zkAPyFDhiwYPvxbfQf+uMbevXv3wd699MTdavS4LOL6UQfIN29WR5MmLfR4SVYr6smVK5d2ZmEhoM22GU4n7d4SwWJZAuA77ULAacK4CqdLK1euhq1bt8Hlaqb/lm7ePIEZM6Zizpx5+OmnH+5zvREEIeYQ6bCd7cbYpeHwYVY3C0LczMQ2aNBA22rRm5hTKFEVvBL6HL/8civYbB9pKUB4TsBu74M8efKjX7/+uHaNRtX/wuE4BqVoXj8NJ0/+hwULFj128Eo91MyZs+B0vuWjyUFKrcXds+evKGn5LPiGU3zUqpUpw0K917TdWo4cuzBmzGjMmzcnTmuXaZ+4bdsuuFwbAExnI2eWKcPh2IM7d0qhcePmurpaEJ5GDezTQKQzsMzaMLOTM2dObbru/SXILzVBEJ4so0Z9ptvG7tr1PIAGUIp63P2w2WYjdeoUSJ48DSyWonC5Znv8mTOIYRMGGxYvfkU3H2E291HZvXs37t69bYrIfFEZVmsCrdktV47V8EJ0UKtWLb1wtoA2Z4kSMQsbtzPi9AmfOHEynE4G9fwb8iQELtfn+O+//LrzI7/zBEF4CgJYZmAFQQg8DCi3bt2qM7xs6UeR/IMai/z228+YPHmyLp46enQlUqRIhbZt30SLFi2QNy+tk7728yfeDHZ7L8yfP/+xAth71inX/KzBKvg7T9RWTIicDRwXIdTw/fJlJltq+VkjH4KCcmiPZAlghadVAxvbifQ3i3TbEaIKty/dH3/8oTu/sZo+e/bseNo4dOgQXn65DTZu5FRmKPSTbdGiJSZO/NKv7zJnQNj1y7vz1z15TyY/7xgEiyUdLl+mpODRKVq0KJInT42LF6cBCC3eCg+zvw49YyMIMYl73sf+PJKdUOqqtBgXAo8EsAHD+qj9tQcMGKAzPWfPntWP/fTTT3pKURACwZYtW5ArV15Uq1YN/foNQ48efbRspUWLVrr44mmBfz9lyryILVtOAlgAgJq7S3C5RmHOnEWoW7d+OOPoiMBOKgkSJNZNDXxzGg7HPu3r+jgwA1u0KG2YJrLZsw5W77HW+NSmx5w53z3W+whCoMmUKRPy5SuknTl886NujxxRj2RBEGJBAPvzzz/r3tm///47Fi5ciGvXroW12YyufrjC08W+fftQqVJVHD1KE/bf4HRehlIXoNSXmD//ezRp0izSThjMAtKvNKY5aHz++ec4e/YSHI6fATTU2dHQLlfd4XTOx7p1q7Fy5cpIbZPTxO3atYHNRk/af72e5fEPQLx4drRq1eqx959ZYIBer6+zXxeAluaZBgCK6SD2zz93xrhxF+I21AC/++7bUIpd7NhC2bOhwwbYbB1QtuyLuvOdIAQU6s8DXcBliZua9kgHsHQgYOedVatWhWsfVqlSpRjlDybEXj76aARu304Kp5OBW2njX8lp9M5wOqdre5tNmzZFaFsLFixAmRde0NPw7BFdKF8+TJw4MdJZzSfF1KnfwulkIOlL71odNtuzmD6dFdKRg+bUWbOmgt3+AuutTTZ2PqzWylob+8UX45AsWbLH3n96zYY2coCx0HJ/BrC6/WWWyyB+/OA4XzQkxDx4A8fvMouFneQywWKpCbu9MIDyKFw4KxYunC/XrSA8TQHsX3/9pS2EvKGN0Pnz5wO1X0IchSb9s2fPgcPRCQCnwb2pD7s9u7avehgjR45E48aNEX/rVl3ONIPtUP/5B11eew3t2raNEVnB8+cpwcnlp9WrBU5nXpw+HSrTiQypU6fG5s2/on37RggOHqm/lIGmKFr0hvavbdeunc/XcUw4yzJhwgR8++23+O8/d3Aa3mnko48+Qv78hbFyJaUCqTkHQ0EEgINmLQavHWGzTcBLL8k0rBAzeffdd3VbzD59OqBevWC0bFlE/31s2bJJ/w0JQsARG63oK+Ji1oY91r2LaXbu3PnAqmlBiAi0+rl9m9N5/oq1rHC5svkMrLz54IMPMJTZSI9sayulwNC31fTpqFK1qn6/devW6YxsmTJl8Morr+gK/4cF2exe9TjZGUoaKB+wOJi1fBNWDIALTUxjAnfnKgW7/Q9kyVLqkd6DX8AMRNmN6fjx49o+ido/f1AW9MorbbF//x5YLDYo5dQNE7p164qRI0doNwHq38uXr4TTp8/A5SphNLsL2V7Ba2ujjfxjN/r0oRZWEGJuw4cRI0ZE924IghBJIh22N2/eXBu3s3cuv8D5xf/bb7/hzTffjNNdXYTAQE1lihRsCrDFzxq3YLHsipAbQUa7Hf19PE6VZmGrFZ3atkWnjh1xeO5cnJw3D7169EDWTJmwdi2ziuGh1nv48OHInjmzDuSSJEqEjh066OxNZOG2qlSsiMH9+6OZ06Gzw0NwCxkxG1bQz9Xdwo+Z6H/8ZksjCgNX2mr5Cl5ZEMfglkVzFStWxr//Mnhfpzt5MaN6925/jB49Bl26vK6zsw0aNMHZs8FwufYDyAmALaWL+vloaa+lWcWKUQsbCj8v2E+bJvIcT3rECoIgxBkkAxswIn3U/NLhlyFbyvKLmP2iy5cvj9KlS2tnAkF4HHhT1Llze9hsU7Ux//2MgtN5IUJBXXmHw+cUAxuqHnC5UNDh0CVOvzmd+NnlwhGlUPLGDbxUpw4OHnRPhUPbTb1YpgyGDRyISsePYzJLk27cwE/ffIPnihaNtPabwduf27djg8sFGlDxSAbqo3WgPO7Apgug3oLF0hpNmzZ7Ik0A9uzZg2bNmiNp0mT6b/mFF0rj1q20cLlWA2D7TGaXOYU6CEqNxuTJX2H27Nn466+dcDjGmcItNjF4kI42KZRyGZ1sqM9t7tz5UKNGDQwbNh6DBn2si2TownDmzBk8Sf7++290794dxYqVRMmSZTB06FA9kyQIgiDEkQCWhVtfffWVnkpctmwZZsyYgb1792q9XFxuSygEDmbzs2XLALu9LICPAfwPwC+6ixTQH/369dPTfg/jgp8pfmY8GVL94CVUyMDJcJcL8e/cwRdfsII/lP79++Pf3bux2eXSr20PaGnCHocDhW7dQvPGjbWsIKIdgL6eOBGvOZ3wrm8OATAeLjhxCfHjj8Nbb/XBjBnfRlqqcPHiRe2dy79RXzpfmrOXKPECFizYCqeTU6cL9HpKdTfFct50gM2WHF9//TVsNgasLASDybyymM63nMNi+UEHrPTSZJb3xRcr4fBhuhZshMNxGk4nNfPfY8uW/ahcubq2FJs5cybGjRunbfkiOqYPgzKKggULYsKE+di5swC2bMmCYcM+Rq5cebB+vTvbLQiCEAVIBjZgPPJRZ8mSRbcnbNq0KXLnzh24PRLiPClSpMCmTRvQvHl12O3M6tNr9EVkyLBeBzfUtkaENQCO+nj8ewAvmfyiNwzfmjudmDltWth0//SpU9HT6QTrkz1hidkYZm5PnNABV0Q4evQoLly54rf/T34G1XY7unTprAulvFs1P4iTJ0/i5ZdfQZo06XSTAQb5zz5bTLfDdMNAtU2b9rh16xk4nX8Yr1a20qRO+Bk/Ww6G1ZrV+O9yPXdQ3NZkant6ecCSFQAWoWfPrvo9X375ZVy6dMdkeEuZ1/GG9yU4HEuxe/cuZMqUVa/Xo0dv/dmSJUuOCI+rPyhv6tKlC5TqBoeDVwN9P2fD6TyOW7dKok6dejh3jjl5QRCEKEAC2IAR6aNmVoSZmJYtW+p2l7TP8lwEIRCwAOnbb6fj9OmTOghh1vDo0YPo2rVrhDOS9EOtYfK3bo4A2Asg+QNexxwji8TSps2IJk2a4trNm6jtZ10qVtPY7dixY0eE9snd2cdfDyzmHK8yOE7sy4HBP5wOf/750pg7dw0cjg+Mhngxdu9Og4YNG+pZE3ehFoNFp5PruN8jpcn/7vSz9Sta80otq9PJzkXuoJK3AAz051BVbLLloZZfFktzbQLfuXNnXUT288+/mQw638ubxfrfu3cZCJ+AUiwM24ZTp/Kjbt2XtCvCozJq1GjY7flMUVm8cGfZ5ZqNmzdvY8oUf2b2Ty+UvfBm55lnCqBgwaK6Gp/tVQVBEJ7aALZnz556YSDLabnChQuHWwQhkKRMmVLrqxk8RVai8ky+AvgHITp/WwB2FIMN2WHBZQRp+YCvCWrmFpfADqpnz549ieXLQ4On8X7eg6HWDaUi3HKSelN60U7xE4Qvo7WWw+GzAxB1uVOnTtU3kNSwejJo0GCcOXMTDsfv2tUAWqBQD0ot13ZWPXq8gUuXLnm8rqLHqxm8tjBH6cuy61ModUtLKUqUKAW7vau5DSDNjMdsEuOgQBkC8Nlnn2LBgvm4efMmhgwZZgxPsvjYNt9vpDGT/8gIOUhxKLUEShVHv36Prq1ftWotHI7mfj7qUsHlqo7Vq+8v2nuaofdpqVKlMHfuJuzfXwW7dxfFiBHjkDdvgce6WRAEIQJIBjb6bLTmzJmDefPm6Sk+QYiJuHWfFy/+ByeC2WYDf+ufiU1jhHw4gXL4wBRPeYaSbIj6Pz0dXsEEVxQV/IHpuAuGQTW93ovNX685nahd21+ONjzMHr8zYIA2UWdYRpcEqkKVUfl2tNlQsUyZcB2A6Lvarl0HLFmyOJymtXLlapg+faq2tpsxYyYcjndMcVW4d9SNDG7fnqa9c1OlSmUeZ9GUpysBu+j9aKb3Bxid6xkzIlPRqFETHUDPnz9b61UPHqTVV00olRU22yY4nTt1tnrq1ClYvXq1LrKjW8MPP/yAGzeYU+ZtBLOwfb32j21meUw9fIxWEFyuXti0qTmOHDmCrFnZ6StyhDaseNDHnD3GNLUgLGZj9p/tgCmlCTQ8HwMH8qofCoeD5zn0i8/pHI1btxpqScWxY4cD0uRCEAQhxhVxRaSARhCiGgYiy5cvR3lTte84cgQVcBEpsNyEmolNCRaLwxLpkK0MbGC51iQdslnB3GJoMMV1bbAiCYJg1f9rDQvcrToYclHN2dVmQ+0aNfRsRESh/IZuHsMtFmSy2VDDYkEhu12HzDmLFcO8BdzXe0VfVavWwLJlv0ApygDYuvkWgJn4+efd2pOVVl63bt3QR+ObdLDbc+LAgQOoXr06goMTmsDUE2ZHfzV5afoiMFh8HjZbaMOI+fPn48UXX0TZshUwaFB/fPnlFyhThi4kv6FOnWz48ccfMXfunHDd+Qibm1it8U2r2aXmPcKtYUQbvqQFCNPlPqpOtWzZ0rDZFvhpFHEFNttKlCvnb9yiDko7qlatrgPXAgUKIHXqNGjcuMkj2bQ9iM8+GwObraS5dfP8+E8Cl2uG1jlPM/pvQRCeAJKBDRiRPuo+ffpgzJgxMaKLkRD3YBEUvUNPnDgR7vE1a9YgZ848qFmzJvb+9Zd+7E+XC+uoD4UTo/Qjn5vp6jUmK5ccv6M0usKCzgB+RnFAtznoB+ig9y88h/XohtuoDRcuQOmGr5x8L2i3oyp/liyJGbNnR/o46KSwf/9+dO7bFwkbNECJl1/W2bHfNm/2yJKGBo47dtAt4CcTfDP4ZEDYEg7HWp0VpRuIGR0/73YbLtdpnVVjk4bevXvCYvnI6ELdPeCvmmM/oqUCDLCtVhtcrhdMqE6ngY04frwo2rRpo1vzbtiwHrt3/4G5c2frxgyjR4/WszOeMGvqctFuq4TpBlbNZGE3mvOw0gSx/vSX23TW+lGbpLzxRg84ndQnf+gVxN6BxdIJVutddOzYEdEJG2mUK/ci1q1jxpt63A1wuUbh++934PnnS2kLsEDAz+wNG36G00nZhy8JSzpdLCkyAkEQYgUqktSvX18lTZpUZc+eXdWpU0c1aNAg3PK0c/nyZX4L6p8P4s6dO2rx4sX6p/Bwrl+/rqZOnaq6du2qevXqpVavXq1cLlfY8xs3blQVypXTY+9eSj3/vB7jX3/9Vdnt8RTwgrLAqkaFhISOfUgIb7PCli6AspvXpkmTXgEWBTRVwHkF3DWrHVdASQXY1GyP13L5B1CZLBaVOnly1bZtW7VixQrldDoDNgaHDx9WI0eOVP369VNfffWVvsaqVKmmrNZKnrsRbrFYmqpChYqqChUqK5vtOY/j8Fy+0se8d+9e/T4Oh0O9/npXZbFYlM2WTAUFFVY2WyJlsVhV//799THlz/+ssljK+9ieSwHNVapU6dTp06fVmDFjVLJkKfX2rdYEKsSMfcKEiVSuXHn18aRKlVZZLA0UcE0BfRWQ3OM8BiuLxa6A9mbbnu91WdlseVStWnUea1yHDBmi38tuz6+A/gropez29MpuD1ILFixQ0QnHOmvWnMpqraCAW17Hf0HZbPn0uY0oD/rc4d9TUBD/TkY94Hqqoj/jhcgjn/kxf+wj+v39JAh773//Vers2YAul//9N9qOK6IwZjx//vx9j1+8eFE/9yg8UivZBg1otC4IgYGayeZNmuDCpUsoEBSEq0ph1KhRKF64MGbMmaNtoAYNHIg8LhdmagUrsAvAiC1b0LB+/dDiLiczSpt1VPSKj4lq0sFMnI8dOxYzv/kGqc+dxn71HRxYAhcqw6KN+dciASy4Dqe2tPKEZnHjlUK9ixe1G0Lx4szYPr7sgdnLvn37YuLESbBYgmGzpYbDcQw9evRC8uTJ4XL519cqlQdnzvyGceNGo1KlyrBYmkApZlfzsLxMuwRYrb3RrFkr5MnDx6DHa/z4cejdu5f2cabuMlOmprqNLovM6CH7999/GrcB748IjnN1nD8/T093h8KM8Cs6axgqcfgDLpcTBw6E4O2330W2bNlw/vwiWCwsKqNtF7O6lENsg91+Wfv+0jIsNMv7hpEvbITNNhwhIacxcuQ9ScWjMHjwYC1/GDt2HDZunK61uXXr1tONDdiIJbqzr0eOsJ3Gt2YcPUkOp/NdrF//svb0zZmTnc8eHWayy5Qphw0b5sLp5Dh7wwzwzyhfXtqqCoIQWA4fPuzT25syOe8Z1YgS6QCWVdCCECh2796tO1+Vv3tXa1Fz3L2rg1BOLjf8808Uyp8fDiNX2W1MmqhXfRVAEwBUu/7pdOr/U33JADeRn/dK7mHR9fv27eDE/4tQ+Bq3sBE/6D+GqqYen8pLuqc+67UNhpIJrFY9zfo4ASynhUeOHIk5c+bh9m1O49Nh4RMo1REuF4/gOG7eHISbN6fCal0Lf3VGVutOZMmSWXfDW7DgO7Rt2wEXL+ZFUFB6OJ2XtHvAyy+3waRJE+57LQMiBne+/GRD8T56ssDcCvC5bmzYa8rPeGuwVbehDYXrfAWl8uLQob2IHz8REiTYiYsX65jkK0vX4sPhuIuRI0cjXrxgKLUSd++GWmqR0qUrYPz42VoT+rhUqFBBLzGNffv2wWKxQSkG9b6g7AJav/y4ASzp0+cNrF9Ph4sPjFTGrSK7Cqv1FYSEhODVV/nXJQjCE+FJaFatMVcDu2TJkrD/s404ZWxuGNBS/sckR5QEsG5YVMEPX8LMDoMCQYgsn37yCVI7nVjscmmfADcsI7mpFN4xJUVJTI38QBO00uU0ramdr2++iq+ZANdfbyW+3ma16ownzOvTm5p7b1Ka7fkicn2x7oe+tizYuXs3JRwOBoGfskkzgF5mjQvGW5WtdJmBZSHPZ6bpgCdb4XL9gM6dGdID9erVw8mT1XXGmn+b9JKlB2z27J79xnxngZkFZ+aVdmD3qv3/9rC1IteNDpczMLM9Pj7otvuqcTBgYFSFIZcpNuMIt8Tt2ydx+/Z0E6gPAXQrh+q63azL1Qp37riLyH5AoULPYtasGZEqjIut8MNcKafJfroz2p4c1/8mScK/gMenTp06GDJkCIYMGQC7/Rs4HHVMMdt3iBfPiaVLl+isvyAITwhaKAY64LQ87rfSk6N+/fphM0Csn/CEjXoYvH76Kb8DH4HIag6uXbum9X82m01r6LjY7XbVrl07rWN82hENbGBJkjChGuQlxPvd6CO/8SHSOwaopIB62/x+1qzbFGAYoAoD6nkfGthDgEpvtargeMF6fRugBvgRAh4ElAVQU3w8t8S839atW/U1sHv3bnX8+PEIH+/du3dV+vSZldVa3mhCRyuAGtD1CriugF0KSKsAahWpG+2ogExGL8rHVyuAeqcRymZLqkqUeEHdvHnzkcd/y5YtKnv23KFjYkuqrNb4+v+JEiXXekjA4XH4U4xu+JAfDeVAFRKSWo99SEgSBfzo9fwlrVMOPZ48CiisgHNe6/yirNYQ1bdvXxUXoP4rfvwQBbzrZ0xbqAwZsmjdckSI6OcOdeMtWrRU2bM/o/LmLaTeeecddeTIkQAdVdxEPvOjj1ilgT18WKkLFwK6XD58OMZrYLNly6bOnTsX0G1G+jagd+/eevp06dKl2hidy/fff68fo0OBIESkGppTCU2bNMHN69f1pDQVgFSgurOvzMe18vHaTCbXN82jsxZh7ftEk4ENzVkB75vtMq/5rMWCsy4Xbt2hCOBnONECY2ANs+N34zBtAPiH4e3CSaViD7sdxYsU0S1tU6VKo6e3M2XKhNKly+ks5sOg3dSpU8fgcvGOc4wx8Hf7zqY3U8b8eRjAQiOY4P8/Mlk6ihxyIihoEFq3boTVq1fojmOPAi2aKlasgqNHeaQb4XRehMt1EcBk3LhxC0qtgcXCprubzdmhVIDZWX/TPZU88tatfLjmJjVniWdon2mccM9xIZRycLm6YeLEyboJwtMOawr69OllMvA8x+x0BuNBzMdn4733BkW6icfDKFOmDGbNmomDB/dhz54/8eGHH+r24IIgPGHiqI3WoUOHwhx2bt3i7NzjE+mjXrBgge4ERLsiTmtxYVMDtqr87juakgtxEU7Lr1y5Ut/Y0HTeE97kbNiwAZs2bcLVq1fRrEkT1KhRA/8sXqybi1J80toEp52MPICqR39f2YVMKOc0RlBUYbY0Rkl8ndsEaJTZ7uTgYF0Y5tRtA74zQeI43EBulIBNmzr9YELF5202LLHZULhoUS1NKG8Lfb6RxYK8nKZJlw7HTp7FzJnUaw41Xahm4fffXahWrbq2vXoQO3fuhNWazCht3zXB4HRz1BVMk9nZJoh1YzNdrjg1n0D7qv7ww1JMmfL1Y00tjxgxErdvJ4bTudJM/1tMV672cLkoYVBInfoP81ywuW24YPqP+eK0x//9FZ5RO5vdbI9NJXzRAFeuXAy4B2pMZdiwYejTpzes1gGwWtMhKCgnLJZMiBdvgp5aa9+esg1BEITYi8vl0p91tERMlCiRtoAkbKzCmDJKAlgGKmnTUj0YnjRp0oRpC4W4A0XYLATKmC6dNsl/6aWXtOaybu3aulCpU8eOyJAunS4yYkvYtKlTY9GCBWCYt9Ph0LXoLPuhUydMxnS7dmAF/PVHYjEX7+N6mqKtIcYm/5jZjlvBybzm66+/jm5vvGGyf55q1xRwYhOuoSdGIQhUAtILNm3lyli7bh22bN2qPU1DKlTA91mz4kSxYvh09Gg8X7oMLlwAHI6tJqhkU4QWcLmYnWyMDh06+/07oEn8zJmz4XJdMuF3dROKtzYerFTesh1zXj9HzjD9uvZVbdiwsXYPeJws+OzZc+BwsNjKVxBcVzc/eOmlWlpkT3P78ePHmwzhPF9bBMBCsRzmd39BrjLZXNcDznDo3TndAuICVqsVn3zyCY4ePYKRI4ehd+8m2lXi1KkTesZLEISniDiagX3//ff19wiLlz0b3rDWYfLkyY+20chqDipVqqSaNGkSTnd348YN/VjlyhH3K4ytiAY2PB07dFBWi0X1AdTfgDoJqK8BldFqVSFBQSqpzaY+ANRuQP0GqASAXteX4I++q54+r7N8rMPtJzEa1YSAGm0e32desx5Qm4wGtkTRolo7yGsTKOfX+xL4Rr+2efPmDzzWCxcuGL/Zj/1s54DezvTp032+vnnzlspqTaSA7z08T28bX07u/3Nm8bef08PGxmKJp4YNGxa27ZMnT6ovvvhCjRgxQi1atOih192tW7fMtr55oCdo48aNw72uXr0G2jMWmO3hEXtaAe309tw+sCEhTf1sd73HOV7sZ512WidMvbAQOeLK505MRMY++ohVGljWTFy5EtDl8vHjMV4DmzNnTu3vThIlSqT+pR+uUmrPnj0qWbJkj7TNSKc42IWLmTbq/goXZrYI2LVrl9bhUdcoxB1Ytf7V5Mk678bspRu6BhzjdIHLpevKi5nHNxlnUuYRfdGQVYkA7uqpgSRojav4FwptTf70Bw/jn4+NUZPbkGOlmQDnZOtJMwk/f9EirR0M7evufvc7JuPoecd6FBZYdUWkrwzz2bNn9fXNLmAOB18f2qr2fqhNzaQ7bPnywGPHKqVoFkZdqRveib5hKv7nmo5Yh/3oTCl/oG/p31CqJBYs+B5vv/02evXqjQkTJmirLas1kbbPSp06Pb75JlTq4wveAXOdc+c2mwywN7dgs/2B7Nk5+veYOfNbNG/eCsuWtYDdzo5e6eBw7EFQkA3du/fxsENZakQcPTzEIHuNS28KI/x4zaidi5rnmZHlVNJUZMv2AooUKQG73YZatarhtddeE42mIAixnzhmo+WGXq+5cuWCL2nB3bv81o88kT5qpnv5BU3Rf5EiRfRCE3I+Fgi/RiH2wOmADHa7Dhq9WWgC0tymXIclKW4L4/vDxHvhy10d7FSHCyfhQDcMRjytjU0MoLkJTqk67eMRvB4zBVvKFFolMAFThgyhFlC8RoETZiu0CMoMYKixhboKG76EggslS7JHfCiUAdBuKF26THo7KVKkQLt27paj7jIxT/jue+BwnNdtVr356aefEFozyQDOF+3M9HxSE1Be9R5tOuoZ/Si3nw83b95Ct27d8cUXE+B0fgClzulCLLZ5OH++KF56qZ627PIFLU06d24Pm436W+9SNjJaH4u3/pLHtnTpYuzYsQNvvdUBnTuXx5gxn+L06ZN6Grxz584e1im9jaSgtSk+Y/B9x5wHikTO6dsbi4U3BK/Abn/GqKAVtmw5hN27n8euXQUwcuR45MmTT26QBUEQYin58+fXtTDesHaqaFF3EiNyPJLILEGCBNHeP1yIfo4fP46CTqfPi+igCVRTe7gLsGTHZoJbFmJ5M0z/yzB3hAnSxsKlFa5rzVb+xm18iBpQWv/Ke7mtxkL/mt4y/QwUbuhyrNCmGylTpkT37j1M8MSMX1qzPb7Hd7Dq7kdnkDAkRHeiIqx+r1q1BjZvps9qW+NzehF//EGdjg0WyzgoRS9UqwlcJxmf1n/0BPjIkaNw584dnR1lVnfjxo349luqe+3GwN8XzBKTrqYaPZOp5KcudoU50qImD10FNttyPPNMEXz11SQoCil0ptPNs1BqsTbHHzRoKNasYX76fugaMm/eQvz7b1k4nW8ab9aLJgv6rd5/d/cub/iB86APHd7c/Pbb7zh69KK5hWHWdbK5DfkdFktNtGrVBBUrVsTChYtx4cIRJE+eBz/+yFuQbnA6Pwu71XE6r8LlaoEGDRrh33/3I316zwI3QRCEWEQczcAOGjRI+8AyE8us68KFC7Vf+fTp07Fs2bJH2+ij6A7YU50966mH5cL/U8cQFxAN7D1ef/11ldVu1/6rniLGI4CKD6h0AJuaqjWAegtQyY12NZHRw3q+Zql5jdXoI+3Ip4AvPHSWynikQmXzWI9+rhbtTWpVVtDjtbQKCWmrxz5RoiQqJCSRsliaem2Hy1YFxNevjWe3qx9//DHsuIYPH278UDd6vYa61VpGv/mKAo4o4A3zO3W2SxSwQgFdlNUaT1WqVEWVKFHKaFYTmPXW+dF9fqKPwWLJoACbWTfI+MEmM/+H8YwN/X+6dBmV1ZrQ+Mf62uZUvd6ZM2f8nkP2pn711bYqKCjU/5ULfUfHjRunXC5XpK8Jz+v+77//VqlSpTOaWfrZvq+sVo6fRVWoUDmcjp7/r1KlqrLZshh/XO9juais1gRq6NChj3Clxh3iwudOTEXGPvqIVRrY06dZOBTQ5fLp0zFeA0t++eUXVaVKFZU6dWpdL1GmTBm1YsWKR97eI9loUUawfft2rYHlwunEQoUK6eeEuMGVK1dQqVIlHHE4tN50qXE07WEyrVbjDEClI3OYI41BVDWTRyxr8n3MiaY3qtBMpo8TnULrYC8s6AqLNrNy62NO6X8XmElod742ERRSwQW7rl7fiAy6ixUzd3dx8+YNKMVsnnee+DkAHREvfgJs2bYtTCvK6vzx4yfC5Wpp7KM8ocp2FiyWeAgKoo8C9Z7Mfo41lfl1zRF+AZdrKdauXY3t2/dQQGCypHYjfqALgSf/mIwwRRRnYbEUMcKIlmbK/ZbJ0M42sgdqeb/DmTMWuFxO47fgi1BHgAu0TfADM9RTp07BmTOntM0Zbb6OHj2Irl27aplBRGHWmllXalXJN998ozWrf/21E/3790K2bOuRPPkYFCt2AZMnf4UVK37UumJqn3hnnj59ZqxevQpO51Gji+3vkbsnyeBy1cTq1e5WtYIgCLGQOOpCQMqVK4dVq1bpuhLK9H799VdUq8bvzEckshFvjhw51MCBA+97fNCgQfq5p524noFlpr1pkybKbgvNElrCZU1DM6LwWvIDKofJsKYC1CeAGgeoFB5Z1CaAuuOVdvtRb9tiulW5lAW1VS7YVEmP9+PSGFC9AFXUPP6KqYRvEMIOR/lN5vQ3BXyogOHm/3xsqV6fFfxu2E0udL9n+K3Ot9le1NX5ZcqUVRZLVq9uVZ5LFdNtiv/fb7bLbGQ603mJna26KIBZ1LTKYrGbjGtP04WrjQKqm+5Xf/rYPrfJ1wz18/4jtWvCk74rZ1ey1KnTmQ5eZYwLQQKVPHkq3fHJH06nU1WvXtMcw+sK2KyA3xXQx4xDLa+xbaLKln3xiR5LbOdp/dyJDcjYRx+xKgN7/jx3OKDL5fPnY0UGNtBEWgN76tQptG59f9Xyyy+/jI8/Zm248LTy559/4sWyZZH8xg2MdDq1Jf1uU2vO1gVOU0/PcqREJtPqMnrYpiYf+D8A75jc5QVj3c8yo3E+iruYE22iM66j4KC+FD/oLO5Bk7VlFnaVyde5YR6UZUeNTAGZFWfhwgumUYC77Ku/ycA2079Rr8rOWu6CJ4vFCqX8eawqWCxnkCxZbhw7dgpKvfiAlguVjV8CjGKXGdrfTCOFz03RFgvNGsJi+Q4hIfGNhyz1siVMZnaPKYDypRrmNpmhprPBQJMhdnMGdvsYNGvW9LGaHRCHw6HvlC9evIicOXPi2Wd55kPhnTT1wlevcl82wOmkC++PvFpw+XI7VK9eC3v2/A+ZM7Nw7h70lu3W7Q3s3csrgkw1GVcqoT8xTRtqmnw7r56rWvdboQLVzw+HRaX//feffl8aZwuCIAjRR/LkyX3O6vExzsbRoeDVV19F27bhnW8eRKTzzhUqVPBZScYvOKaHhaeX1zp0QPpr19DS6cRPpo5/gwmtlOktlc6UIZUzE+V2Iytg2dQQU8BV3AS8rDlnWPM8G2H4ec+6UHDotVmqFVq33sWICVg65W2sxFDnZfN/7oNLFyWdNUHVBbNw7/n4YP0nkD9/IfTo8QbmzTuB+fOPaBlBqCm/L2uPX+Bw7EWjRo2QNGliWK10N/AHn/MMHqeYPab8gEF1N229xYA1U6bUuHHjuglE6XKw3FTqZzcj5Q+G6RdgsdQzxV68pfgCNltJJEvmxAcf0J/h0WGHlEyZsuliq4YNG2rJULFiz2PrVhaVQXfgu3LlOpxOikg8LVKywOVaAnYMpMWXJ+xWVrVqNezdm9AE6+tNV7KlRoByyhTOMdBnq4vbsFg6wGq9o4sA5s6dq7u30AmFzTI8YTe4okVL4JlnnkGpUqV0AFujRi3s3s1xEQRBiGbiqIRg0KBBumlL7dq1MXToUL3w/3yMkjV+Znfp0kV/p0SYyKZsv/zySy3AZeHWt99+qxf+P02aNPq577//Pmx5GolrEoL//vtPjRw5UuXPl09P9duMFCCjkQ+EGet7yAkSmnXcz3Ga/0VAzQDUKEDlNY8NBlR7QBXy79yvxpttZDbvzUYI75gCMZef16wwEoIBWkJgMw0GvFc7aKauuf1KCjju8dxCXVAF1FfAUY8CrhXKbk+rihUroae/p0yZYqb3//ZZdAQkMdPhno/fUMBkLRkIlQ68qPeBxv3ACwoYZ+QLl8z6dRTwvF85g9VaVT3zTH6VOzeL3kLHymq1qfr1G6qDBw8+1rkfM2aM2ebLCtimgHO6UM1qfU4FBydQ27dvV8WKcd+ah+1PSEjodc+foY+1V7lz5w8n0UicOJkprPOWXhwx4/Kq+f0tBSTXY04pBGVKKVKk0fsUFJRJ2WyJ9f95rFevXtUNHCwWq7Jay5tzSNnFZGWz5VGJEiVVf/31l3raeVo+d2IjMvbRR6ySEFy4oJTDEdDl8oULMV5C0LBhQx0jejNhwgT9HBk7dqwqWLBghLcZ6QDWYrFEaLFareppJK4EsNeuXVOvde6sbFZrWOV/EKCqAaogoFID6ktAnTXdsahrDQFUsPn5JqBWAWouoKqYwGqMiVTumkCUAewi89w2H9EZA9QSRj/LAPYVQGUB1ICHBLArTQBbPCSR0ZL6i48bmQD3Ix/PTTPPWVVQUGFlt7M6Hqp48ZJhVf3sQJcjxzPKbs+mgDUe3bV2Kqv1efP6N31se4MJnscq4GsPdwHPnwxuByvgO/P7Dz6280tY5y86BjBA++2339SpU6fu67o1a9Ys9fbbb+sgkJrVh3Hp0iUVEsJ96Orjfa8rm62QqlKlmsqbt1C4de4PYN9SmTPf08Z/8803Jug/6OecUKdMNwn+nTVR8eIl1DfI8+fP10Gs1VrT44aB3cS+0YFs1ao1tOOBxVLfR2B8Sdls+VTlylV1d68rV648ksNCbCC2f+7EZmTso49YFcBeuqQUP38CuFy+dCnGB7AJEyZU+/fvv+9xPsbnyIEDB1SCBAkivM1I553p3xWRhR2MhNgJK8Pr1KyJKZMmwely6cn7RkbXynrBv81kb2fTFuAfMxFeyaQAfzPKzypmSn+l0cb2MZPqdrMua+pra0v+0PX+9NiHq2aCfauRGCjjCcBGBgUBnDbyBV8sMj+va4WMr45WbrKZ6v5PvSreSRvthcDOUk2aFEC+fEkRP34CbN/+OwoVKorBgwfj9u3bWLduFfLnp8dpZdjtGREUxCn/okif/gw6dGhr9Jz0WFgMYDWgHWyrGR+GmrDZ+mnNbaga+HcjWzhhjv49ANvMKNU32l3KCjgdPgRWaw2UK1cBzZo1w6FDh7TRP6fQf/75Z71vbq1pxoxZ0LJlS3z22XwMH/4FSpQogUqVqj7QnYCOIrdu3TS9z7xJAKezD1avXol8+XLDbl9h1M7eKNjty1G06D3NLH3/goIoo+A4+aK8cV3YDItlMT788D2tT54xYxaUygmXa5G5Ygg9fFvD6ZyKVauW4/z501BqmA9NclI4ne9gzZpVCAlJoDXBqVKlQ//+/bWuVxAEQXiysBnQ0qWMHMLDx/gcuX79OhIndnsMRYCAhthxgLiQgZ0xg1PYUJkAVQBQPUzGszKgknlIB/hYUi+pwGt+0p2XTdZ1mPn9F/Ma+sMeAFQe8zszrtVNFtdmsrzZAdUSUBdNhrez2S9KEY56vc88VsKbDGwWnUF87gEZ2NIKKGP2f5WP5zuqXLny6ulnu53+rIOMt2oXZbMl1FP3586d09m8devWqX79+qm+ffvqqWxm+twZR2Zp77kyMCtcVwHdlc2WTMWLl0BZrSUU4M5Yei6DTTX+av3a+PEThm0nODih6tatm5Z4tGr1ipn1SKCCgtLr5znV/vnnn6t48YKV1VpNAXvMNpmdXKhstpTqhRfK6DteeuD+/PPP4a7VHj16KKs1qQIWGTmE977t1O8zefJks08jfWRg6eMLtXLlyrDtfvTRR8YX9oafczI/1JnClkVlzpxdXbx4UWe6KYsIdaPw9RoeU2KTufV3rveZ/aQf7WwF9NCZW0oveA6fFmLz505sR8Y++ohNGdhLly4HOgGruM2YnoGdNGmSstlsqm7dumrYsGF6eemll5TdbtffI+STTz5RTZs2jfA2HymA3bJlixoxYoTq06eP6tWrV7jlaScuBLCVXnxRpTeBUiITLAabwDWNkQJ8a4JKBpmlTLMCrj/bfwShSgOqhYc8IJl5TVZAdTUBcjyjo01gAtk33Ob6JuhtbX4vb/aF6zcFVB8PG62EJoBlIBsatHzvY3dCLbRCraxgNJPhgyKbLZNKnDi5stlKeGhS3cteZbOlUrVr11Zz5szRQdrt27e1NpbWUXxs/fr1yuFw6AB38+bNqnXr1joY5vvRYuq1114z7z3Lz5D9pwNYi+UZlTp1eh2s0giawab7+mvSpJmyWnmcEzyCwj3KYqmr9aA2W3YF3PSx7dCg2HPhewwePFg3GQj/XAKj5fUMsmfq544cOaID91DtbWUVEvK1HvuECV/Sj3Xv3iPcdD2ni0K3OdHHPjkVQP2qTWtrDx06pF/DADP0NQseEKBmNdKE036eX262sStcUGu3p1Jt27ZTTwux+XMntiNjH31IAHs5xgewhN+NzZs3V0WLFtUL/0/Z26MS6QD2gw8+0NmevHnzqhdffFFVqFAhbKlYsaJ62nkaAlgWvIwePVoVKVhQpU6eXBXKl0/f+fCYmO1KniSJDiI/BtQ1821/yWhPPbWsXFabzGtDE+B+6ifCcJlAtZH5fZ3Z1geAetVkUxko1wFUb+Pt6g6g3jQBawKjv3Vnez0zv0HGV5Y62b4mgGUgmxkWZdFa1B4K+NV4wPY03azqKWCO2Y5nIRa1rEM8AjhqVn0d1ghT7BW6XpIkybQO0zP4y5gxq5o7d27Y2DOYoyaVP1kEFbre1gcEZmmUzWbXQas31LyGvn6qj9dRH2ozvre+tstjzGWKyI6afWAxll1ZLNlMUM3M62GTeaY2t5V57W1lsxVX5cpVCNsXHiO7joWYsS9ZsrQu8PSlNWXG2GpltvQrj+D6mAJa6yD0008/Dfc63gQkTZpCAb39HAv3M9hkq/v7OdbqHp7Ans99oLPUzPQ+CN6YsGNMx44d9YcuA30G7zGNmPy587QjYx99xKYA9sKFy4Gu4VLcZkwOYHle2rZt+9iFxY8dwNJtYOrUqSquElsDWAYBzBAym/Vs/vw6c/qimfJnEwH+HhIU2qLUah7f56O5QHsjHbjt8RiD0iQmkMzr9Zx7WWECulEmk5rOyATcbWh/YiW+WSelcTFgQFwYULcAdQNQuc3z1UwxV3qP18zzeK87JoiqGRKis7x0LUgeVhzlLpRiRvGsgm5ZazPT7JP0tLfNVlavV6NGDVPp7h30uBd3ALncuAdYTPMCFlddNsb8LCqCmj179n3n5PTp0zpL6jsb6Q7qbOq9997zeU4HDBig7PaUOqD0HcBy3755QHDMTGtTj985JswQn/Cx7jdmexOU1fqiLqjydefM4r+HXfdsG9uiRSsjFUiqgoJyK4vFptv+UnLhi8qVua+UUOz1EZz2MsH6G+YcDDSOCco4ULTyk2XnEnoT8aDCNrbbZUCub5bseZTVWlFfFyxUZbAdk4hpnztxCRn76CM2BbDnz18OdB8DxW3G5ACWJEmSJOABbKSLuOjZVaZMmci+TIgmWNBTp1YtxIsXD/Hjx0eurFm1dyZL7H42bqf0ZuXvN+/e1Vb4VvN4HuNaOszDEfV1U0C10RRadQSwxFjys6HpvwAam58w211sPGJZWjPelOnEM89xG5sB0MW0iCnkOm8WNl/dC6ATgBDjL2sxzQqWm9efM80OuHjD5giXTQOFU3BgJ4ua9H5wTw+ENQto2rQRiha9bN7pdZQubcfixYu1R53LxWIoFjP5wt0SNqV5t8pmz8oZ/9eSZnQbo0eP3tq7tFChYsiVKz+aNWuuC6UKFy4Cq/UTM4Ke8PPoA4SEhKBnT9/m/ZcuXYLFkt6MpjcscEprzrIvrpsSuWc83m8agA6muYI3rczjryFbtlPo1+9tfPDBhyhWrCQaNGiEZcuW6eJNXmcPg6bVs2bN0AVdQ4f2xRtv1MfEiV/izJmTPpukkDt3HKb8r7RxIN5krrw6ppVGY1OM1980Lua4pDLetGwtPBlAAx9b/k//y3H2BW/yGzVqim3bWKq4Gg7HHrhca+F0noDL1Rt9+vTBd99999BjFgRBiMvUr19ff68Gkkh34urVqxfGjx+P0aMZXggxmalTp6J9+/YobLVihMuFQww8b9zAHyZYZAC4j9WBJihlyHPbVPl3Mc4CC0wAu9383x3aXDBW83+ZcKKR6ZvE4PdXY6+f24R4bCMQbF5bEcBY0/wguwn7fjN15by03eFPItMAIcSElQ3MPjLMumjWZ0Ca0U9YAtMkobgJkDuaY4bZr706+AHixQvGuXMX8M47b6Ju3bqw2Wyw2+06cPn333/NXswygZ03bEyQyYzaXhOee1fAM+R+F+fOFcXw4Z/A5WL3r+v499+lmDdvrlnHZhobDADAm8OjZpS+wyefjPfbSYudS5zOSSaMT+1jjfwAZpjQv5jH4xzF983tA/uWEYcJ5nj2fcF9LIjSpbPB4VAYNmwYbLaycDoL4c8/d2Hx4rq6YcD8+e5jejg0rn73XTYweDihAWx143vxsWmLAXMlcfnHOCG8bzwvFprboN3m/N3fAYZYLJORNWsu5MvndjYIDxs2/PzzWnN18gbFDStlR8Ji+QvDhn2oG1v46jIjCILgicsVugR6mzGd3Llz47333tMdL4sXL46ECdnI5h49evC7NpJENmVLLRinVnPkyKHq1KmjGjRoEG6JLYwbN05lzZpVxY8fXz3//PPq999/f6okBMePH1dBdrvqAKixHgVTbu2o1asRgc38ns1jXVb/1wJUXY8CrZke2lWuv8ljPnabcSVgwVUDQNU0rgLBZvvclxNm3a3mvWC2M8nPPPctI0/w1JVSDlDLFIXx9yler3FLCPiTmtlOHs/dDbc9TpenNPrJIJU5cxY1fvx4VbduPRUUxOIpi0qWLKWyWlk1v8RDSsDp+U/MNkabSn0YSYKvw7hrnqfv6yYFxFdAOVNIdd28PmO4Y8ySJYfWkD4ITm1Tv0lXhPtlDofNsVGTy/3vq4B1Zhq9pnmfEV5T8Sl9NF5wLw7thZszZy7tnhCqJ/Z8/geta2Uh55O47rt06aLs9vSmkOyKkW/8a/b7ZyMdaGgec5+jadotIn16Nj1IZMbZaZ6/ZrS9oU4K/giVaaTx4S3rXubpbfDvLSYQ3Z87cRkZ++gjNkkIzp69rG7dUgFdzp6N+RKCbNmy+V2yZ88eNRICRsnr1q3T2ZOUKVMiadKk4ZbYAFtR9u7dW3t57tixQ7fHrF69uu7r/rTAFqDxlNIyAN7XMO/HfOINIw0IMROtzGNZjcvoQUBnac+YyeTTJsvJ7CvpZbKtFU0z1CzGqZSTy0tNhnOLycwuMQ1bd5mcmcXkK5kxDTKT98dMZlY9wK01dCKcr65rMoR/4CLa6MawV00L2m/8vJbZ4f8BqGp+dxhv2tDJemYsmXVzGd/RQjh27KpuaffDDwdw9+4HUGo8Ll8uBpeLo/YS7PY8sFiqm1zym8bZtodpoEv8tSt1P87p7HdMlnOVyeglMKPP9rFtERQUX3u3Hjq0Hy+/7G6K6xv+/Y0e/Zlus2ux1DZnYas5s3TPTWLy4Z3NFDrPXEMA6zxEGW4sZj8mG7ddb2bC4Tiqs9JO50cmU+xJLbhcXTB58tf6N8ol+LcVKF577TU4HKfMFZfIjGEOs9+8khRCQjimuRAUlAM2G+UTr6J+/VrYuXMbKlemrKMB7PacsNlehM2WERbLMN3OsF27dn7f9+bNm7BYkvrIrLsJ9S+8xZ65giAIEczABnqJ6dCr3N9y8CCjj0cgshFvokSJ1LJly1RshhlXdvfxzCpnyJBBffjhh09NBpat2SqbynzPDOQsk+HraJ5jJrYYoBw+0kvfmnW3A2qcR8bWncFkIVZ9QBUxv78AqP/Ma7sAKpdHt6wzgGpmtkE3guFuz1aTFX7PTwb2nH7eYjKInk+9qeLBqqaZ9x7sUXDmzsAWCgnR73XHZHJr6HVtpmjqjkemji1TLWbp5JGlcy+hvriVKlVSjRo1UhkzZlYWSz7zWnf2Mo+pdPd+rcsUStFHdv9DbLNO6mKmr7/+OsLnmZXw2bPn8OjgxYWFYem9irFuGi9UtmutbI6V3cLWmmxtRY8CtyzGJutSmAuBxWJXL7xQyqxzxU8HLRZipdBjnzhx9rA2r3S2CAT8+wwt/CpnzuE0ZbHQScKiXn21rS4gYxHYO++8o95//331999/h72WrgYsOqN3bqtWrdTAgQPDbLoeBLPgoce8388566MdEugsEROI7s+duIyMffQRmzKwp0/T7UcFdDl9OuZnYJ8EkdbAsmNCzpxUR8ZO7ty5g+3bt6Nfv37hCtOqVKmCTZtYGBIedjRydzUiV65cCetWxcUf7ucetE4gYUeh2bNnY+/evUiQIIHOHJ0MCdHlRztNhpOZzzsmczrTlL8sNT2feAPnfRPHXB1H6XujztwDYLop6BpltKncJoyOlflC9p76zmRyc5usJ0wWdqrJRaYz+cv2RkFa1uT9WnuVD7mVmszCBmEcbPgFTnxucr29YMFE3IQDw41Od4rJMV4OCdH7cSYkRGdpmWe9bDpzhaCv2UuYI7Eaxe9xU7o2xKiDPTvJNYXVOgO3b9/E5MmTsXv3btSqVQdOJ4u83jHZzp7mqJjFfNvoMveZkeIou7WqIWaPfF0XqWC3Z8CZM2cidN2cOHECxYqVwK1bNxAS0sroaK+bHmUrTeaVWlCYDCJVxw7Y7YeROnV2nDrFs8TMrd1kpL8xutm3wml+2YHsxRdrIygoyBQ7MevpuX8zTWb0HYSEMCO9CUFBuxASshSrVnVHly5d8dVXE/G4sGCKWtUxY8Zh82bqXIG8eQuia9evdLaaf8ctWrBc8B6e48gOZFz8Pe+v8CB9+ky4cqUPXC5qaT2L1P6AzTYdXbp00u8dVX/rDyKqP3eEe8jYx/yxjwnnJq5qYMnx48exZMkSHD16VMdinnz2GWcTI4eFUWxkC4OWL1+ufzJQim2cPHkSGTNmxMaNG1GqFJuThvLWW2/piv3ff2c7z3sMGTJETzN6M2vWrFh5/IIgCIIQF7lx44Zuq3358mW/xbFPCia/KLM8eTLw733lyhVkyJA0Wo4rolAa99JLLyFHjhw60VawYEEcPnxYF0wXK1YMa9eyWDZyRDoDO3bsWK2DS5s2re4Tz6yMJ4HUvcUEmKmlXtbzQsmcOTOqVav2wAuFd3qrVq1C1apV7xujQPLLL7/oi6KFUjoHxjwalYJFTfaTWdS8Rg+61OTg3Pc9VAnSkaCHybB6c8bk4z4yVfz7ATxnMqIDAXT3Wv+OyfS6jDnTah8GT9+ajO9es1+ztXoyVHU5wGhn3ffIeUwukw4HMLnFMrDhMKpB4YuwHC9ViJ+Y7C5r4DeFhODXKVMwpF07XLx5E2d19Tq9Cl4zngg8Am9YsZ7TIzftzQhj7EV3gF9htc7BM8/kwunTJ3Hp0kUzUlZzZMxyxkOZMi/oG6NWrVrjypU8cLnmmDz1JTM63vvxFoKDZ+DAgX0P7Qd99epVZM6cDYplctq6yxuehWdNjny1yUHPgMUyGi1aNMfYsWNQtWoN7NxJlTI1oG0AfAhodbHTvK6uya1fMnZc+czYHDAKZ15NfwMoZfL0FRASchdTpqxCu3ZVcfMmt3EDVms2fPjhe1rHGlv5888/MWrUaJ09cDjuIl26TOjQ4VWtmY5JN7JR9bkj3I+Mfcwfe/cManQSVzOw/fr1w5tvvqkTgvx+o41kmjRp0KpVK9SowcoZPPkAllNqsZlUqVJpqyRO03rC39Olcxfj3IPeqVy84R9JRD6kIrreozJi+HAUuH0bk5xOHT7R57SpCRxZUOUp9thtpthpqLTCFFAlMlZWrb3WZVp+sAllWppw5ifzezJT4BXk42Ji0HzFBKbhTTJC+dFDVmA18gZ6wGY1we0HplBsvQmLPI2JkpmguQsWaSFEEK7jF7h0WNnSyA8oR3DfVuy7eRPFbt7EUdyA0lP6FFTwLu8lH3u21jyfxMeROcz0enXzTlzaYufO8rBYkkCpXWa0OMVcwxiKXcKqVd9g9erqGDp0CD78cCTu3MkOp/NF3uaZcrIh5igPm+B4GoYPH6VlOvocKKVnOyZMmIQ9e/5BsmRJ0bJlU7Rt2xanT5/GjRssHGJxkr/rq7wpx6MnKpAgQWL07NldW5nQKmzp0sW6ECxU1lDOjOBHJlDdZYJ1nmm+z+8mIHYLNQoZ71W7Gbc04faDwWtoAJsUdrsV169fj9Vf6rR9mTHjWzidTj31RS/bmGyb9aQ/dwT/yNjH3LGPCeclrgawe/bs0TJHwu8fyhwTJUqkv4/q1auHLl0o5XvCASwr92MzNFrnlxHT2e5gnAbs/L1bN+YGYw/Mwq1au1arK13G8ZN5NJhg0FupXADAIOOSmc8ErztNHq2kCQ4rmgzuBBNETjGB4z6Tg6xnHDH/NXm7YJOnq25CnEsmMGUYxBypp83FCvNap8kQc3++NHlR963DVePfSrt6XxQKC69H64ztCyZsPG/2t7rJDpcy+/o9rFBao1rUHOU7JswNDRJDOWOCyRCjZV1ksrvkohkZBpmhf3yhMPPZCYrGYjrDOdu0WLjXUsHp7AeL5WW899772LZtC+bPn4/vvvsely6lw7Vr+3HlCgPdUFKlSodhw74My1IyUHr11bY6aLLbi8DhYMB7HNu2vYWPPx6FuXNnmnEIfyMWHsplMmDs2FE64Cpfvrz+wHDDQDllyrT477+vzdlY43HbUclohUuZ3Pswo4vNaJwORpnHYM4yHQAK+9iH7XA4LqBAAW4/8mzbtk3rjg8dOoxUqVLq6T/erfMmNDrg+/preiAIgiD4hr6vbt1r+vTp9Uy++3vh/Hl+gz8Cj1r9tW3bNl2hy2XHjh0qNjFnzhzt/zpt2jRdqdypUyeVLFky3dozNrkQnDlzRu/LMx7+rm6ngGt+qvpPmOfZlrWV+clK/ZcAldDDh5StXBsD6itAdQZUAkDlB1RX41xgM21ec5n1nwW052oqQE0w+/EcoMYb79gWZh8zhb0H/UtDt5OZXpyAOgyoXoCKx37Rfvaf2+brhng4HHBZ6rHv6YwLQduQEGXRVfVHzWptjQtBWgUMMR6eAxSQyrQpjedRzf+CAqoaz1Yuc33szo9m3fwKYDW8r10+p6zW+Orjjz8Od+5YFc/2pXPnzlUrV64Md52war9p06bGTWCml8frYWWzPaPy5SukMmTIpAC2ur3g430P6dcPHjz4gdfQyy+/bI5hpZ/953G7x3aox+P7zP7VUkAjM6YHVUhI6HXPn8ANZbVWUBkyZFF3796N1LVNZ5BOnTqb9q10RWigbLbC+vcyZcqrS5cuRfKvJW4glfDRh4x99BGbXAgOH76sLlxQAV0OH465LgRDhw7VDjH16tVTkyZN0o/16dNH5cqVS7vFFCtWTLcKfxTwKEFTxYoVtcl78uTJ9cL/02Lo7NmzKrbw+eefqyxZsqh48eJpW63NmzdH6HXRHcAy8FmyZImqXqWKShAcrIPJIEBVNDZW7mDDXwB40CPQrQ2oLYAqZB5L7xFgurfL/2c0tlvNzO+egS4bFzQBVBqPIJr70c0EvJ4BMX/mA7S9V2KP58p5NVXgew/0se/XAZXXNFfwfu5/5rW09RpoAtgEISEKmGJWofE+13lXAR0VkMAjkE5qgjEGseVMUAhjNRXPWEn5Gs5Qe63QZbKfdZSyWivqgDQi7N69W6VLl8kE0q/62eZ6/Z79+vUzATntsP7nYdv1uwJyqZCQJA+9TkePHm32320r5r2E3iQB9U1DhMvm8Ram8cENBZxSQE4FJFchIf1MAPuZslpzqvjxQ9S6desifZ1/9NFHZvwneDQR4LGtUTZbUm3PFRu4ffu2OnbsmLrAb5koQIKo6EPGPvqQAPZyjA1grVarjhv//fdftWvXLv0YA9rOnTurQoUKacvPw4cPP9q2I5ux7d69u566ppXQhQsX9PK///1Pi6MfqRVYNEG5wJEjR7RFFp0HSpbk9HLMheNL0XOtmjV10daJtWu1eTrVnCeMgpMmYL8Y3SjlBL6YaXQjFjPx/bwp8HIXSR03mtjtZqIdZvtfeRgyFTfb4aRxF2PYlMQoRQuY1443tlulTHlTQjNBvdu8L5WV7iaiL5oJ+hXGAKqfmZzubqQLN0yZUnnTZJW2Wd64vSO+R3yM0uIIRl1UAtOagxZOg42ogqVuk0xRExviXjMyAVpzlTR6zqtGN0pl7h1zxCxm8kSZ7VJKYDWj5xulruip8BEjRtynvfaukK1SpQbOng0xoxneEuoe5WG3p9PWTcOHD4PFstMY++cyZXQlkTjxf9iy5beHVqRmykQjMxjhiC/cjzc3Y/WDEYEsM8rpECMA2WQELDRLIwORIsVV/P77JlSoUAGRgdNMlEmE2oBxccsFeNVWgtM5CosXL8SBAywmi5nQ1o7Fn6lSpdVFn5RrVKxYRTeBEQQh7qJvxQPcxEBFyksqanEbXdF94Nlnnw2TE0yYMEEXxzKuyZo16yNvPFIkSZJEbdmy5b7H2Yo1adKk6mknqjOwnHp9++23VcLgYK92qqEtW297pcxcpoUr27eu8nr8BzM9z9d/aJoOdDa/bwTUJUClBlQpmi2b1+0D1CJA1TFZ0nZeU/dcdpvmBilMs4B+Jov6hWk1y+0v8JOerG1a1zq99nWkkS14HjMzvJW81lVmv3Oa9wQaq5CQSyYLyExkNfP6INNy1XeWNLRRAsc4s2nx6p62362AMiY7+495jAb/3c12KyighgKK+2jn6n49lNWaS1mtIcpuD1Jffvmlz3PNBgahWcdVD5nWdym7PYPq37+/ft3FixfV66+/rkqUKKHKli2rvvrqK+VwOCJ0fV25ckWFhDCz+raf92K2Oo1p2sBsb2eTpbYbGUb49UNCbpixf1WVKFHqka55fpaEHv8mP/t0Q1ksQXoWJSbCbGuePAV0pjh0XH9SwNfKZiuhLBarlo08KSQLGH3I2EcfsSkDe+jQZXX+vArocuhQzM3Acob+Sc3OR7qIiwVPvir5+BifEwJLl9dew9QpU/COUrpxQCqT8Wxmsp+eNlXzTKGV28isplmf5T9/miWRKfCiU4E7p8WyHLcj7mLT4DSTsbhPbNwH2MTVboqzvGuv85vynvEmXznMWPa/bjKvyUxBlS86mJwej+djkz88Z3KjzLyGwr2+BgeexVr8qfevl2kk+rsxfzoMK1x6NBbAYlljMoEjjP1TEpNR5Zb9sdVU2y/1KkbKb/K/2YyJWCFYLDtgsdxF69avYvp0jmZ6uFzMXfcw7+m2VTpgKvWzweViU9sbcLkG6GpLehHXrcvyt3v88MMPsFrLweWqYM7Kdx6NcD3ZCIfjZJiPcbJkyTB+PEc/8tDOpG/f3njvvWGmsO11M96XTPaaufdRxpXAabLO+UzmlWPlXdQZ+pFit/+MEiXuFalFhnsG1/cKzsJDB4Cg+4ywYwq0iTlw4ASczs3GLC4Up7ONLupr27YDatas+VCrNEEQnj7iogvBM88881DXFs7mR5ZIB7CVKlVCz549tR1ChgwZwjoC9erVC5UrM7QQAgVlGpO//jqsUt8NvRNCDYru8bHpn1TdeKEmM5ZVdBOYY9Ydbabl3bqRvR4mS2NM0DjdhCmvmGn9P80Ee2ljj0WfWV9UMOHOdTPh+5Wp529vJqH91YxzP8lChGABbiIpLPp9Qo+wv/FK7Wc8D0I7pW0wjgNurKgGlw6yGHYPh1K0f4IRJXQ3brLvmdE4FWYrdQ8e4a8mjPdVSZ/IHAlH8BQslmC4XDeROnVqbNz4m/YHXbhwEe7eHW/stiqZ0dxg7KUKGVcDBti1YbE8j2HDPrwvgL19+w5crsTmz7Kr8VOoYTxs3Zw0+2JH8+at0KFDWwwcONDYYT0aHTp00MHz9u3vGBOzrObs3zUBKoNa7iv/3teYoOw747gwOVzXrtBkOeBwnA7n+8qbW0peWMH/sA8yVqbGixeMO3eWGWmEN2vhct24r6tWTIDHOHnyVDidXcMFr6HYoNRI3Lw5T39+durEW1JBEISnm6FDh+omDgEnsinbo0ePqiJFiqigoCCVI0cOvfD/RYsW1cUKTztRKSF46623VGq7/T6ZgDIV/+U8CrM4vf+2j+n9TaYYK5GZhmcB1Q5AfWSm7hOY6XebKbRqDaidHq8/a4q2chh5gcPPHPxEj2KtXz0en2Me3+3ndX31e7No6rwCOpip448V8J9ZxWkKhDi13sY4B3xu1uujgOM+prGb67FPkyaDAlp6yQgKKeAvr2r6UqZYq8kDJAajFBDisU+f6W1OnTo17HwdOnRIF1blyJHTbM/9vqye76+AbsbxIL7piR3e9WLQoEHKZnMXSt01+wOzf2+bY+F2UyjgSwW8o2y2ZCpnzjzq3Llzj3SN/e9//1MpUqRWNlty46RAaUAyLcUAlitglgJKmLFbEU7GAJQ18ohqpthqrEqUqLwe+08//TRs+y+//IoKCgo95tSp06uBAwc+1EWgXbv2Zp/+9DoP55TN9qx2YWBBY0zj4MGDD5F/KBUUlE+98cYbT+T9ZRo7+pCxjz5ik4Rg//7Lih/9gVz274/ZEgIWcT0JIl3ExYIEdttixuaNN97Qy48//qgfu1cQIgQCmtU/o0JLkTy5ZZw5md/71uTAkhqPV+/c1gum/CadyQ3+bBoZ9DeduI6YiW6WNXFCtq8pZ4IpI7pk8oAHzQQ8JQbeOEzhV3aTw2Sx1UsmK/yzyaV2NeVRnrDYaxxscGpxREqPTF5VD59Wq8kKKo82Dd3NVoeaqXZv6F8KdOnS0eSfKTRYYFo7XDYZ0QJmyWP2pJrJwvrrlb3eTJ2796kXLJZ6GDHi0zCROjvTDR8+HC+9VBc2m1vswaztTtOi4XMz4szQWnHqVPjCqY4dO8JiuW2cei1m3xcaCcRoI2XgWf7HCESscDrz4N9/D6N27do4e5ZdxiIO97tx4+a4fDk9nM5/zNndacr3Fpjsb0sjiVhjxsiNJawMr0ABXhmvwWp9A2XKJAwr9vz111/x3HMlMWfOBty9y/2egXPn6mP48M9QqlS5B04ZffLJx8iXLyus1hKmWIzZ7d6w2fIgadLTmD9/doxsJMDihFD8Fes5oNS5cH68giDEHQJdwOV6ApKEQPIkP6cjHcC6d4gt2/glxaVKFaoshUBDiQar8BnSuPnLBIhO00SgtQlg6SaQ4AHT+wdMKPKBhwtBK6ORhQly+foqRkvLCfcspiXsOyZctJn3m+vRTeuIee3/jFqUE/7KOA28b2QML5lgO495bFqYRb4Vd3SDgSEeR8c98+6Idt48ftHsPZeEfnp9IUzowJut0BYP35qmukVM6D/HiCJOm0DZYULtUybs9mat0XuGn/JVqhX27v3ffYFjoUKF4HSeMf4KFFJ4/gEnMPpcK1auZIB7D94Afv31ZFgs38Buf9bsCyULJ81V8LsJGlcbScI4M91fE1u2/IEcOXJj/XpPccXD2xBz/53O0R5XQkFzrCfNVRXftIllpy5vduqp/s2bf4XD4dDtHJcu/d5ICBxo2rQl7tx5Dg7H/8wtE6+4L+B0bsE//xxH//58zDfJkyfHpk0bMHz4UGTLthl2ey+kSjUbb7zRFrt2bX/kxghPGrZGLF26HKxWylV8fat8B4fjPBo1cjdIFgRBeHpRT9AiIcIB7Nq1a5E/f36fvYQvX76sv1A2bGCYIgQKjul5p1NnR2ECR2ZPj5uwqJdHadKxB2znlCm5sZkcp8OEclQxutlj9KvpTED6nlE9rjJ5v5bm6/imeT61KaJi1nURQqBQASuQXa+f3QTVzGcOMjpavn8Bo6hk8DoDmXAX78OpM5uJzLt/ZjKLzA274d5O0m1QkyULgdXKrU8yuWF3uZo3oVZFO3fuhN2e2ZSjuYlnSsa+MuH0RaORvYr48UNMgFjXZCCXm1K5mia0D83sht8WdKco/n24ixibNmWW2GUKuHzB4yuv7du8ad26NX79dQPq1s2HePGGwGrtgTRpzsNmy2xssjiaL5tjOGmuCnYOO4mbN0ugdu2XdOY+ImzduhU2GzW3vmyueCUMM3n5T3w8fw52+3i0bNkCCRIk0B2qaOvlhsH5qVPH4HKN9nGjkR9O5xv45ptvH9ibnFnKt99+G4cO/YO7d+/g3LlT+OSTT2L8TM+gQe9C0ddDa5VPe1zH82CzdUbNmnVQpIh7nkN4UvDv8bfffsPChQv139qT/CIVhIgS1zKwLpdL39hHawA7evRoPcXpy1eS4tzOnTvjs88YgAiBgK1E3337bZ3/6mlyf6+YsOWICSmGG4/WFiYAZcAIH3KDr00oZTFFUzzp8U07WPdHuttRs5d5bKGpN69iCsOmmbxh6PopED9telhz5gIsCeDSedZ1cOAgXHhT7x9zbc+agPmgyer+ZEJG4tBbrW2m7JeZDN8/xq/AvVfMPr4Ci2UvUqdIgkuXzsPl6mEytQyKmhpnWE8OwW4fGzaGoYGYv8s8vXmv12CzpUCfPr0wdepU5Mt3xIxYTROKc/SXeHk+wAS5dgwYMEQXMObOnQ8bN27U08ihwRzf3zdWqzNcwOdJ6dKlsXDhd7h9+yYcjrvo3r2rkT4wCzvW7PcUr8AwJVyuebh1y6ED6ojAftSKqma/solEZnzeN8H7FjPe02CzlUbSpA4MHsxblPv566+/YLczUPcXqNXErVs3dDvBJwUlCtOnT9cODatWrYoyl5Tq1atj2rRpCA7mVZ8JVmsqM5bNULjwM5g1izMCwpNk0aJFyJHjGZQtW1Znu1944QU880x+LF/Om1JBEJ4GIhzA7tq1S/cg90e1atWwfTu1hEIgWLFiBY6ePKlDFroOTDbhE4NKTxMzi9G2JjK16gwl3V/T+81rmYF90zy2xjz/j8l57vByA5hgQklO+3vT2tRVW3AXZ89ewOnTZxESwun8irBaqevsApvt17CwLYnVqtWU8U0gy3CvjzFlSqT3mhX/yU3G87CxqqJxfXYT+GRB/PjfI0WSxLh64oQ58u4m65jHBFPZTTU89ZvvwGYrjvTpE4ZZd7hczFief0CmlrnkDLBY8uDkyZN49dVXsXv3Lu2ssW/fPu00YLXuMLcCnjDrOcvkmJml3IDDh1OjcuWqOnirVKkyrFa2e/CV9TkKl2uDntGIiFynYcOGcDqvGOnDCpMDv9/KjjcWLlcd/PRTeGnCgwItuimEz8V7Mh2pUqXDmDFjkDbtKtPogZKFtqhcOZeWDlD36wu6DSj2TgsngPEkVP8aHEwhTHgYaB47dkw3Ggm9CYkcfM3bb7+D9Okzok2bNujevZf+fGJAExmJxePwyiuvoH17Bv1OhLoVVofNVhA7dmzTGdhLlziDIDwJmHFl0Hr0aF7T2oV//2vw77+ZULt2HQlihWglrmVgY0QAyw5CvvxfPbM55849yGdTiAzsbhZksehyowVGmVjbj+rTbia6r5l10pnpfepXtxtFI7Ohl4yeNZ1HSMcckdNMIqc0mVxfasd7fZD4flehVFJcv/4ubt4M9SJNnnw3ChTYjBo1Uuvsx549e9C5Tx+kqFcPNRo2RKr8+XVOM6EJpunqatGCiI+NPpVT+aHZuODgcyhRIh7GjPkUXbu+jrvXriElfRZ0CP+dCVy3m9C8s3mM4fsnaNasJtavp0YUaNGiBWzav6ufj0DyL5PFZGjNv/4DSJcuXVjQSP0xA+ClSxchYcK/YbMxUO5qptXLm33mQh0n960sXK6VuHs3k/ZUZTbX5aK37AAPxTDMyDOPbsPIkZ/qjO3DyJcvHxo2bAyrtatxx70/6LtHMO7e9Xy/B2+3Ro3asNl6eOXvnUZiMQlvvvmG7rB3/PhhLRH66aefcOjQIaxY8RNy5aKkwX9w7HReM1fY/VgsXyN79tzIk4c3IqFwivfLL79Erlx5kSVLFh0cZ8qUDR999JHW1EaUXr164+OPP8adO5SGnIGijwc24dixzKhWrYaWTjxpxo0bZ7x5WeRHgc/3cDp5M7UGW7f+jZYtOZ8iBBrevHTr9gaUqgellphPM36yVYJSy6FURfTo0VvkBILwNBBRuwLaZS1atMjv8wsWLFDZs2dXTztRYaNFS6S6devqzlPscFUdUAUA1cC/x5MaAKg0gPoNUF0BldhYZPU2Vlbvmc5d7k5cr3t0uKI9Vi6PbldtHvA+dYzlFjDC4+EVymqNp4YNG+b3mE6dOqWsVnZyekkBXyjg73CWTBZLHZUpUza1efPmcGOWLWNG1TRsX2f67EoFVDLdomwqYcIkqkKFCmFjP3nyZPNadtP61lhBvamgrbuKGMsqWkBB7d692+e+HzlyRPXt21dlzpxDBQfzdbSEmm7stLz3Z7Q+zqtXr+rxCH3v1Apor4CmptsX7aHWKputrMqYMWuEumaxd3SdOvWMnVg+P12/biqbLaXq1atXhK+1//77T5Uo8YLeT5vtOW3dZbdn07937NhJOZ1OFRk8r/u6deubblRLPfb3ugIG6+2z85gbWmK9/npXM17NFLBEAT/qcbNY7KpBg0YRGif21Ga3q1ArNu/xuaVstkKqRo1a6knC/cyQIYsCWvv5M5rxwOvtcYjrVk6rVrm72G31M/Zr9fP8nAk0cX3so5PYZKP111+X1eHDKqDLX3/FXButJ0mEA9hu3bqpggULqps3b9733I0bN/Rz3bt3V087gQ5gGSDMmjVLlS1VSiUKCVHJEiVSiYKDVbDFoloab9fSJoBj4HjSx6cyvVmzAio/oP4G1FFADTftZBmQaq9O85PbWmaC4ZTGJ5bBbx9ATTNBb7BHK1nP5d+w7dCL9ILX05207ypb3/piwoQJOhC5/3X3gmDu359//hnudWyhW1G/LwPHO35eO9d8aYUGdyEhafXY16xZW1+vP/zwgypalMGZOxDmtt4wQfQgZbHYtO9oROjSpYsKCqKvq78Yn0EX1Msvv6yCgxN6vCf9XfMoYLgCzph1t+nnlixZoiIK29CGbu8DryDWoc8Bg+d9+/ZFeHts8TdgwACVMmU6ZbUGqZCQJOr5559Xa9asUY+C53XPv5GKFauE3hjZcyqrtaL2raUv4NChQ8P5uP7yyy/muL70Mabf6+dmzpz50Pf/6KOPlM3Gcb/q5/xM0u//qL65EWHXrl3mWNb52YfbympNoD7++OOAv3dcD6KmTZtmxt7fZwX9pqG+++67gL93XB/76EQC2MtxMoCNsIRgwIABuiiC06ojR47E999/r5cRI0boaUA+9+67ob6QQsSg1q/tq6+iZcuWiP/77xh08ybiXbuGTLdu4aBSmGlat/5mFJckm+ma5Z4A4+RoNzOpztKj/Mb+it6thBOvbYzNldMUdM0y2xtjfGKHmaIwTgjfNK+paPpeuSMwKgcrw2oMocYa7aonDXD27EkcP06PBPh0qrBaE/p4nZvQyvLPP/88nO4xYaJEWqnq0ipdfxIWt2sBrbD+DpMirF//G9q1a49atWphx46tWv9YoADFFFeNr2p+BAd/gt69e2HiRKp/Hw6ntl0ulrz5q57foNuczp79A27dolhigymJKwNoUzRW/bsrMovDbk+DP/74AxGF3a2GDKHt2Luw2YqaAqtB5ux9hcyZs+DwYeqJHw6lAEWKPIcPPxyN//6rB5frI9y8WQ/btu3S07D//ccGwo8OCz7XrFmpx71Dh6po2jQt3nmnKw4cOIBBgwaF8wecMGEiLJZcRhLiDRXZZdG/v/uq9g9lTFYrO4b581l9Rk8fP+6xPYh7LW79tYoNgtUaHGNb4cZm0qZ1fxZQDOWL0MfdciFBiGpEAxtAIhPtcnquZs2aymq16iwGF/6fj7EDTVwgkBlYTqGyg9Yskx5YYeJFz05Wnktn0xWL6xQyU/2pTVZ0MqCumSxqCtNlq4uRDYRO+Yd25OLPeFZOsUI1tVh0Nna9kRqEWK2qfJkyasyYMcqus5mUJVhVSoSuH7qd7n4yGwv1OsePH/d5rJSfhMbCu/y8npk3vqdFd25idm7SpEnmNTXMc//6ee0Ak1W9bjpxhY59SEjo6w8cOBBuX7Zv364zNfPmzVMXL16M1Pnn8YVKIQb42I/TymJJqCwWZlv3+JA6dDcdrU6HTWlbrQnVyJEjVWSZMmWKstvjm2x4EjNGHyqrtZyeQo9Ihql06XLKbs+hgGNe+7pb2WypVIsWLaMsC5UnT0EFdHxAZnuwslji+ZwB8mTs2LFazsKOXb6386my24Me2gnscbhy5YrJvg/xsw+/6ety+fLlAX/vuJ4F5HGz2xvQ3IfMxqmlStmy5Yq0NCai7x2Xxz46iU0Z2J07Lyt+JQVy2bkzbmZgI91Klly4cEFt2bJF/f777/r/cYlABrBFCxVSda3WsE9YTuWn99EO1t0ulm1e+d5FPSQFDGL3eq17HFDZPALVJAkT6va/TZo00ZpQBgFfffWVeiYHg5fQbaVImlS9/fbbWg5C9u7dq1566SWVNGlylShRElWo0LMPnBa1WBqqPHkK+G3v6f5isVhq+Zje45R6dgXUNzpVaC1b+vSZTfvU60Y32sDHa/eY1qddwx67F8BeURZLgkcKENeuXateeqm+Spw4uV74/3Xr1unn3nvvPTMW1DhuVADbh05WVms2ZbEEmRa3vgKXC6YdrVs/TB0t1N9//x3p/atVq46y2XJ5tNy9JyXguUiePLW6deuW39f/8ccf5hgW+NnX0cpms9/X7vZJfYnnzPmMAmo+IIBlcGt/qHaR0oDQtrW9fWzjP2W3Z1XNm7dQTxpKTUL1vzu99uGistlKqOzZc0sQ9YT49tvQz5DQIJbjf9toYuvrpMvChQufyPvK2EcfEsBelgBWiLoAllpRbucrj2+3gYBK5xXA8v/vADpTm8QEr6ncBVc2myplsagLXut/YZ7/5ptvdBDjK6g8ceKE2rlzp74RYQD1sMwWt1GoUFFT4LPb4wuZx/HJfUU5vqAWlUERUFQB3yhgg3ktA9W0JsPqVHZ7PlW1alXzJbTRQwfJ1xY2RWBLTKDITFfOcIHcvQD2jrJa06lBgwapyEBtYqhuk0E7C7GGKbu9kH7sk08+0WPxxRdfqHTpMoXdAPCLsXJl9z4vfkAgRi3uq1orywCHhVmRheeO7wd85ec9qO2Fmj9/vt9t8AYmNKvtTyt4SG/jp59+ipIv8UaNGinoTP8BP7pFZtihfv31V10kxcK6o0eP+gwC3ecPaKGAX82xTFM22zMqefJUav/+/epJwwxv4cLFldUa39zocIbhbWW3p1FJkiTXswBPAgmiQpk+fbrJxN4rVuUN8ZPQvrqRsY8+YlMAu2PHZfXPPyqgy44dcTOAfaRWssLjQxN7LjRFclPB9O2hktPNZ0YH+77pu0RH0hPGD1Y5ndhhtyOrzYZ2APrSWdVux+sAenTvrr0o48ePH05rSNumyhUqIGPGjChatCheKFkS/d9556G6SW5j2bLFyJKFVlYFjO9rK9jtNOx6E2+99RbatvXuVBUealHXrVsLm+1vo8wtZyyuCpi2pdSnBulWm7t20eaKUM/o1kH+bLSyXc3v1LFeN20T2BLWm/1wuU5ruyh/nD9/Xjfg6NSpE3r37q2bAPTty5HsB4fjD2ODNQAOxy5tQvbmm29qv+MuXbrg2LFDejzZdYrjt2TJYtOcwF9fNKdRK9MDtxaee64AZs6cjsjC9wq1AQq1MLuffLDbkz+wSQBt70K/1739bd3c9FjvyUNtb6irXw2jG3arvHeax5RuW7tu3TpkzZoTWbNm1XrknDnzaLsqT1skniOex4wZaVFGJ2JaoL2KihWzYdOmXx9o/xUo2Nzlt99+xocfvods2TZqC7SkSSfjtdea4o8/tqNYMVrICU8KfvadOHFE276xOQn/Rvn3Ki18BeEpIroj6LgsIahRtap6zmYLy7jyZ2FA5QbUIYDuldoai9pXX2myYYCKHxSk+vTpowrlzatyZcmiGtavr6fffWVd+Xg8u10Vs1rVdONAMJ4WWjabSpY4sfrrr78eevyUGFA/WqtWbVWmTHnVqVOnSGeTsmd/xkgDmMnldB+n3Tkd/r4CxiugdpgeFpjq49CvmgysO7uSxmTZvDOwnEpP5XcqnRnjePGCtWbSbi+u7HZ3RjWhyfp5v69DT0G/+uqrfo+tTp2XlM1WwExber9+vtl+J2WzpVWpUqW7T58bEZgxD93OD36yp+e148PEiRP9boPZy1C7qfF+tvGOtiSjHVhUZKF4vebNSx1sAnNsmY2shP/PoKv2c+bMqR0jQjPYPPYlymJhlhWqU6fO913zzNRScrBy5Up16NChSO1PoPEnrQk0kgWMPmTso4/YlIHdtu2y2rtXBXTZti1uZmAlgI3GANbtWdgTUDc8rKoyG32r2z5rh58A9qx5fsaMGQ/db36ZZ8+cWVWxWnVg7LmdS7TgstlUpRdfVFHB4MGDdfFSaADLgKWJj6lsSgZYLJjaRzB5U1mt5XUxRtu27bT/a6hGdpC24woJmazHPmHCxH4tqn78MdTuCujgUfDjNO/LoqhqfgK77ipXrnx+j426cLs9nrJYGITvM6+5bfSunAavZR47razW7CpTpizqzTffVBs3boxwkMP18ud/Vlks1fz4wb6n94EWWQ+iefOWymbjPq3yeC23N0dreamJjsov8X/++UdbsYVOu1O+UU4BJfV1kCtXHnO+vvNxvJRDQOuW4zoSREUfMvbRhwSwl+NkAGsNpCXUsmXsaS9ElCpVquiOPZ9brchks+mGqJ0sFtCIKiRBAvxu1mOzU1+wvwzFATdueAoRfLNmzRocOnYMH7hcup+VJ0lpyuR0Yu3PPz/R3vRuXn/9daRKlRQWS0Vj2jXOh0XWS7BYGsJiuQK7ndOtnxozr0mw2Z6D3b4F33zzNaZM+RrLln2PZ55JA4vlQ92yM7TdLPDTTz+gbl22qb2fYcOGw2rl9PIkAOxVT6xGmjAVANux+urYdPOB0+rPP/88vv9+EZIl49ljl6mM5gy2Nr3RepvEcVq4XANx/PhRjBo1HaVLl0alSlVx8SI7kuGhco7hw9+DUivNdg+ZZ/jaD2CxDEGvXj11G9wHMWnSBJQp8xyAqrDZOMYtYLfTiK05GjWqj2HDaLAWOG7evIlvvvkG9erVR5Uq1bRkY+/evWHP586dG3/9tRODBvVDtmw3kSTJbjz77F2MH/85MmXKBJuNVmS+poDbw27Ph4kTeS4FQRBiLmKjFTgeO4Clp2P//v31F0yDBmznKUSGrl27Yt++fWjXqxcuVaqEkFq1MGHiRJw6cwZz5s/X64Q2Rr2ftSYUKlCAGtIHs3//ftgtFpTw8zxDOff5fNKkSZMGGzasQ5Ik9MEs7uGLGh6l6sLluo0GDV6A3U6tbEVYLK+hevXs2LjxV5QrVw7z5s3DRx99jJMnTyNZsmRo2rQpVqxYoV//wgt0ufXtFUotpMvVydwCeFPPeMu63XfdUG87HxaLAz/++KO+afOn9T158hheffVVo1x260zZ/rYKlcqAvj2pYtpfsqXt99iwYScaNmwSoTaX9erVw/Tp05EoERsF54Tdng5WazrYbEO0r+2HHzKYfzCJEyfG2rWr9I1n48bPoHz5M2jZ8nnt2zpv3twHto6OLAcPHkS+fIX0mCxbdhFr1iTB2LHfan0y2756XhuDBw/GoUP/4PLl/7Br13Z9w7Nv3344nVSJ+8ICh6MC/vrLn/en8CB4vW3evFnrwKtWrY5WrVrp69vTj1kQBCGmYX/UTMr8+fN1ocRvv/2mAwkak0sA+2iwqMTzS9xN48aNUaNaNQxdvRo1XC54Wm/TRr+fzYZCzzyDUqX8FfPcg8GdQyldJJbex/PHPIpPogI2xGjQoD6+/XYn/H9PXtXZxpkzZ+hr7syZM0iRIgVSpkypg0c2KZg2bSpstgpwOvsDuISFC2dg+fKVmD59mt/3vpex9h04AzaTl/bMhvL/7F9/Hfv2KdSuXRvlylXADz8s0YGgN8HBwTh79qwJkHuawrMMphCNhWGVdeOBUPj68nA6p2L9+no6mIjIOWWhCotSFixYoJsScGx4zUTGpN1ms+lj4fKkYCBUvXptnDjBsfgbLldoUZ3TyTYcQ3UBILOv9evX97sNjvGpU7wZ8I3FcgrJkiV5Ivv/NMNz07FjJ0ydOgV2ezY4HMVhs/0Ps2aFXt+c3WBDCkEQAsOTyJi6JAP7cLZu3YrOnTvrL8jRo0frLBADjC+++EJXEd/rgiIEii8mTMCdVKm0u8BgAIsBjDBuA/8EB2Pqt9+GcxnwBwOUBMHB+NzHc8r01sqeOTNKlPCXow08zFQ6nazu99WJSsFmm4bKlavpTCC/RBnkMHglnIpm8Ap8C6eTvbreAjAcDscBOBzV9Dr+puPTp0+PJEmSPyC3TaeA/bBY2LeMN2X1TfDJ91kCl4suCiuxceMOtG/f0ecW2JHsp5+W6yl9YCSArEYmwazrGuOmwKCbwaY7WK0Duz0jFi5k566IkSBBAh3I8gayW7duMbLDEDO8Bw7shcPBHnCejhDx9fhYrRUwYgR7wfmnRYvGsFrnMX/u41n2oFuGZs0aB3zfn3aGDx+OadN4szcZDgflQ9/B6eTf4yp9fbdr1yFCQXBEZg0EQRAJQbQEsM8++yyaNGmiAwhaB+3YsQN9+vSJUPAkPDrZs2fH79u3o2GHDvg0JESHU4OCglC2WTP8vm0bihfnFDwilIHt07evtuSisvGSefyUUYwyNBg8bJjOyEUVzLhlz54bNltT0wLWzTW9V07nNrz9Ntux3s/o0eNgtTJr+LLXM/Gg1Cj9v5kz2Yz3fuLFi4dOndrDZqMZ2Z9ezzpgsbyJBAkS4e23+8Bm+9FYOTFryi/4miarWhVO5yf47rt5Pi3IZs2aBYsl2DT69YYtdXvQEMv8dE/VW2GxpMG1azz+p4fly5fDbs8L+BSwWOBytcHmzb898Lh5g5w0aQLYbNWNmRxh0LQJNlsNpE+fAW3a0JpNiCi3bt3CZ5+NhVK8Rtt7fB3w+q4Cp/NTLFz4nc7ue3P79m2dxMiVK6/WhAcHJ0DLlq0i1RZZEAQhSgJY6jTLly+PihUrIn9+FnoIUQX1xV9++SUuXLqkp9EvX7mCb2fMQN68DAoizpAhQ9D3rbfwns2GdFYrsgQFIbPFgqnBwRg7dmyUBwDMrK5a9RMyZXIZb1kW6dSFzZYRVusETJgwQRe6eeNwOPDnnzvgcvmbcg4tXtq2bZvf9x44cCDy589lCoM4xf89gC9hs5WAxbIQ06dP1Vlrp/OOyXu/a7KlnrRiDzLtMekNz5PNltnIA3zhzkR6FiWdhsPxv2j9+6LHLW9MqVUN1aLeH7xElrt375qg3R8JPdbzDTPL69evRoYMvPUqjqCgHAgKygagNHLksOjnokr+8rTAJMSlS+eNJ7MvWuqvCLem3DN4rVGjNnr37ouDB1n89xXu3BmI+fN/x/PPv6C9VwVB8I1kYKMhgGURRp48ebSBOwMqmoXv3LlTMrBRCDOHLHKhvvJRoMn+iBEjcPTYMYz47DO0fustLVE4ceoUuncPrdyPanLmzIl9+3ZjxowZaNQoI+rWteCdd7rj4MF/tVzFF7zmQhsGhJrt++NBbgGUJPz663q89VZ3JE8+W8sELJauqFIlPdavX6e1pXfuMHjFA4KvYFgsdo/17sFGEU4np7b9uQow88v9c8tu+An0FuLHD9KSgOjIxjVu3BTPPfccxo6dg5kz9+ODD8bo80N5wuNMEVOW4nTuNNIMX3yPbNly6VkCbxkGNfZ79uzR789ZoIMH/8HixYvRo0dD9OzZVAdL27f/rsdbiByPen2PHDkSv/zyK5RaDaUoC6HMoD8cjt1wOKqiWbOWT90sgiA8jYwfPx7ZsmXTMUXJkiWxZcuWCL1uzpw5+nv4QXULUcKjeG+tWbNGtWrVSoWEhOiWln379lX79u1TcYFA+sAKj06VKtV0T3lfPqghIbv12LOdZERgW98zZ87cZ9p/7tw57aca2u7WlydsqJcszfK9OXXqlGmbO8DH6y4pIIsCcitgqQImK6C4biwQEU/fJ0G7du2V1RpsGkvcNft5TQFD9TGOHz8+Qtvxdd1zXBMnTqas1prawzf8WCzVzQlGjRoVrlFD9eo1TbvcUK/jAgUKh/P0pRcu2+HSD9e9TokSpR7YPjcuEJnPnfPnz6ugIHrufuzn+v5Jj+umTZvC+UnTq5fNOHy/5rBuQDJp0iQV15DP/OgjNvnA/vLLZbVjhwro8ssvkT+uOXPmqHjx4qkpU6ao3bt3q44dO6pkyZLp78IHwaYwGTNmVOXKlVP16kW+FXogeSQbrUqVKumM2alTp7SP6dq1a/V0NjMkghAV9O3bB04nfVr7eLVDPQKbjfZViLArBjO1zGwnSpQo3OOpUqVCixbNYbPRkmq316vOwG5/E0WKPKe9X31NeQ8aNPD/7d0HeJNl1wfwf0aLLaOUDaVsZJYpGwRkiyCKWDYiQ1AUXiqblyHKnn4scYAvskQEhLKnIkv2LihTpsiUIrTJ813nbhPSNi2FphnN/3ddoW1yN3l6k6Yn5zn3uWM3AZZ2XbJYTU7X/hDbtOyv2Iv0qe2KdOlOYf36daqFkbNJplMW8pjNY2Nrio02mblhqvvCp5+Ofe62SjKvy5Z9Dx+fbTAai8SWY0yFXv+q6rvbvHlztQBNSLa1atUa2LTpD2jaVwCOqQVaJ05kV4tGFy1apLKxsnK+W7duOHlStomV7Xi/xv79/qpOX0of6OlkPUPM83usnef3DRiNYShTpoLKzNi2oLtxQ7pBJNa1Ij98fELU2Tkicl+TJ09Wr6GyBbyUrUnJniwK/uYbaeton/wNkL9RI0eORKFCso28B/eBlZoz6dEotYZST1WnTmJ9GokcS1q3TZw4ETrdVBiNUmsaCp2uEXS6wsiS5W81Jl26dKqV1dixY1UwKy2m5syZ80ynN6dOnYJixaQmt2JsraBsutAbBkNxZM58C4sXf5doGY0EU/I74ePzHYBysbW5sl3F49igazp0unIwGIxYtWo5GjRoAFcIDw9XuTOgcyIjuuPq1Uvqd/x5yc924MA+dO78KjJnnoMXXhiCcuX+VhtRLFu21FruERbWDw8eZIfJJH1y31W10RIsaZrUYb6Nnj174YcffsDXX0twOw+aJvXJUnLxLsxm6SrxGT755BNVy0tPN2XK5HjP7/+LfX4XQ0DA31iyZEGc57eUMcW4m8g9yu6Od9XvHhE5twb23r17cS5Sr26PlAXJa6TtGhMpy5Ovd+3aleixy2urJHu6dJFFn/CcAFb6cP7000+4f/9+gttkoi5evGi3lymRo0jmTWpvKlasot4pSh32iy+WRL16FVGr1g00amRQuzYdOhSzeEuasefLVwBDhozEypWRWL78L7z3Xg/V+SC5GSLprbp79w6MHj0SBQrsgo9PGHLk+AFhYd1Vk32pC7fn008/Ve9qd+w4jqio+tDrYxbc+flJlvd0bCDbHtWqZcTWrVtcFrwK6Yur06VLYsFZNutrQErIfMgbiNu3/8LDhw9U7aq8+7d0vrh8+TLWrZMG+v2kb0a879ZD00bh7t1bahc1g6FqIouPBsBozIeZM2el6Fi9heX5PWbMJyhYUJ7f/azPb1koGX+hqIyvXLka9HrJ0tiri96K6OgLeO2115z2MxBRjODgYJVYtFwS29Dm5s2bKpsav/WpfH3tmnSLT2jHjh34+uuv8eWXlv7lHrSRgfzhkQBWTvfZWxAjq9gvXbqkdpYiSg0DBw5UC0j0eunzKn9A9ThzZikiIsLRq9eH+PzzaSpbZFnN3qFDJzx+/Co0TdplZY3NMp7D7dtvo0GDxvj994gEC4cSa6I/YMAAdUkOOc0tXQ6A4YiOlh3E0sW+Qz6AqKjXUapUYXz99RfqxUIK6F0tJCQEZrNs7rATgHRliG+DyhInFqwn542H9Lb9/PMZ6myNj48vmjV7Ve0YVr58ees4eRMcs1jsySnruIrCaMyCs2fPw2SS9mP2GNRCogMH4rdHo6Se37KZhFySY/DgAbGLN6TF3Sc2i8D2wGjsiNKlX1JlZkTk3I0MLl26FGfjEUedCZHEpSwuluBVSuvcRbIzsNJTs0+fPoneLrdJc3mi1CDv/iR4BSbBbF4fe7q7E8zm1ar91fTp/4eNGzfG+67ssaukYzY/iFEQJtMK3Lp1K1WerxKAjR49Pra+c0Rss36LCoiOnofjxw+rbKY7BK9Cgg3pAmAwSOYzfnnFBRiN41QJxvNsVCLz8f77H6jyjV9/jUZk5FDcvfs+Fi/egUqVKqsd/WyzezESa90lWYN7sS/KMWUi9v2N9On9nvlYKXmkFll6wOr1U2Ew5IFO1wQGg7wRqYpixbJhzZqf2J2GKBHyHt3R5QNa7MkQCV5tL4kFsBKEypkvafdoS762txnOH3/8ofqdN2vWTJV7yUW2Mpekpnwut7t1AHvmzBmULSt7uNsnC7hkDFFqkFPCRuOL8lbJzq3vwWgsgxkz4p42jo4OVRsbJBQETWuEVavCHX6cUnN77Ngh1ZzfvldgNOZVdafuQmqfpJbX1/cojMaQ2L3elqnT8RKY5M6dDtOmTX2u+/7+++8xe7b8v3wJk+nn2B3TPkF09GmYzW+jXbsOajGoZXthWTQkgVFMW7H4psNoNCA09E0YDNL6zF4t82XodOFo2dLF7V3SuN69e6s/WtLyrkULP7RpUxrLly9X5Tuy0x0RuS9fX1+1CdLmzbIrZAzZnl2+treNuZQSHT16VG1UYrnI2XjZF0A+l9IFtw5gpXm8rEBNjNwmY4hSg5wSllPD9p+yOkRHN8bBg0fjXR+3q0BcmfDvv/YL3FPiSc/MxB5bB50ug93esa4kK8337duD0NCa8PGRzgNvIWPGOejVqyP27duNPHlkK91nN22a7Jgmp5Pjb0lqhKbNgMlkwFdffWXt7zt27KfQtG1qUR5wKnasdG+QzgKf4OOP+6Jfv35Ily4Kev3rsbuZWZyEwfCaWl0vGzFQ6pIzCFLrLeUh8+fPV2UFSfVeJiL32cigb9++qiRAzkRK9xfp8f/gwQO1LkF07NgRgwZJCRxUn9jSpUvHuUj5nZQfyedPFne6aQBbqlQpbNqU2N7xULsRyRii1ODvL6eEbyUx4mbsmCcMhrg7CD3xL4zGjahUSXYRcizJPmXPLhkoKW2w5zSiok6p5v7uRhZZfffdfPzzz338/fffuH37pjpVLKtOn9e+fXthNiesm4+RGWZzXezZ86R5dpMmTbBkyRIEBkoQWwIGQyB0ulzw8RmHQYMGqoBJtldeu3Y1MmWS1mSFYDBUhtEop7BLIkeOv7FlywYEBgY+9zETEaV1oaGhqpOPbFRTrlw5lUmVbb8tpWKyJsFydsxdJfvt8rvvvqsidglS468wXbVqFT777DPVV4woNbRs+ToOH/4MZvNf1q1in7gDg2Ep3nor7sKemFZMsmKym821Uiw0ECbT3+odp6NJBqpXrx4YOXK0OkUO2LaWi4Re/wEyZ86uakLdlbybflKPmjKy+CsqKvHuBTpdJHx8AhK0H5PTU1JfJTsAyrFIDa7t4gHZ1vrPPy9g4cKF2LlzpyqDqFevn9pBjS2ciMgbF3E9K+m/benBHd+2bZJESJz0DveYALZ79+74+eef1R8WqYewrEg+deoUTp8+jbfffluNIUoN0nB50qSpuHfvNZhM0le1aOwtZ6HXd4K/vwE9evSI8z1dunTF9Ondodcvgdksmxr8C4PhO5hMhzB9+gxVc5kapFvB9u07sHWr9NhrAU2rC+AajMZ5MBpvY9my1c+9HbCjSTsx6TBy4kQEAgIy4q23WqrfZUcdX6NGjRAe/h2io/vbOeEjW+1uR+PGMxN8nwShEsgmJX369Op5IRciIvIuz7SRgey+JX04ixYtqoLWiIgIFchK2yC5EKUWOY29ceM6ZMlySZb7wGh8CUaj7IBVBJkzR2D9+jXImzdvnO+ZNGmiytBVqvQQen1v+PgMRsOGQapQXTbgSC0SfMkp7unTP0eJEmdgNP4HmTLNQOfOTXDw4D632PBDugNIfVOFChXw1Ver8fPPuRAefhedOnVCSEh51Y7FcTumnQAg8y2tuiwuwWBoiaxZc7hk9zEiIm+ugU0LnrniXrIzciFyNlk1efHiWbWyfevWrSoIq1XrPbRp00ZtbBCfLAqS2+QS01805jpnnYaXIDk5gbIcm7PbDsnpH9mhDBiP6Oj/qJeCmBfBYzh//jW89loLtaI8pcdVo0YNtVCge/f3oNMtgclUHzqdbIayCZkzZ1NvSuJv4UtE3ksWg0sNvrymyyIhohRnYKXFwrhx49QfJFmAIk3lU7ozD9GzklPbsjpy7ty5KgiTLe3sBa/xSSDmTr0pZYu/adOmoVixUqofX/r0mdTqz2PHjqX6Y0vAPHasbMMrZRX94r2PLY3o6LlqF6an1UAll/wfnTlzGh9//B7q1r2NRo30+Pzzqfjjjwi1eICISHb0HDx4MHLkyK16kUofU9lwRkoX0xJmYF2QgZVFWiNGjFB75fr5+ak/vtLz8ptvZEckIkquf//9F02avIbt27cDeBOa9hEiI6/hu+/mYtGiJQgPX4V69eql2uNfuXIFp0/Laf1PExlRB0ZjkFqRKn3+HKFQoUKxGV8iooTBa82adXDixBmYTF0AyOvOdWzd+gW2bHkFixYtTDNnft1pEZfXZGBl14WZM2di/fr1WLFiheo8ILtzSWaWiJJPzmT8/PMOaNpmaNoStRFDzLazJxEVVQutWrVO1bMbT/o1J7ZblWSr/djXmYicQtrjnTjxO0ymXwHIRibS47k7TKa90LRW6Ny5iwpyiZ4rgJWeYK++KttjxpBMrJySlWwOESWPBIUzZnwBs/ldaQYV71Y/mM0zVP9V2y1WHS0oKCi2V+3KREYcQ1TU7zhx4oRqbE1ElFpkU5c5c76GySRdjMrEu9UATZuo3tDLgty0gCUELtqJK35rHR8fH0RFRTnwcIgSd+3aNZw7d07Vj3ryz/DXX9IcOm4v5SeKwMenJPbt25dqxyC9aj/8sCf0ein/2RjvVslySDuyAGzY8LOqQZOSByKi1HD9+nXcvSub1CRWNhUEo7GkU9YHUBqtgZWFH7I9o22TcPnDJr03pR+jhWwrSORI0tB+1KjRaqtTkSlTILp376J2EEnNVarynJdA8sKFC6qJfs2aNVO8VeaTLfcSOx2mQdPup3ozfulVu2PHLmzY0AhAYwCy3asE1vNliRmA9epd/e7dNdSCudTY9IGI6Mki3BuJjDBB026mmW4lrIF1QQZW+kNKL86AgADrpX379mqPdNvriBxJ6q5ff/11HDggL3JyCmk97t3rgilTZqNWrbq4f19aMjmerMAvWbIMKleurBrqy2KmfPkKqT3fU0J+h8qVewl6vexiEtPaK67NiI6+lGC3O0eTQHr16pWoWrUKAFnl+wmAxQDayvYGAKrGXprjiy++TtVjISLvlTVrVtSsWRt6/RwJxeyMWIXo6Kt48803XXB05M6SnU6SLAyRs0+39+7dRza8g9n8uVpcFKMhTKZ2OHasBiZNmqS6YzjSL7/8ggYNGsFslgBuA4CXAPyOq1cnqhZeUrMlraGe1+DB/WNX1A6NvVgWU/0Go/EdlClTWW2VmtqkBEjTDKoTAvA/u2M0rSrOnpVuCUREqWPo0EFo0qQJgK4ApFtJjthgdjUMhnfx8sv1VfvOtIAZWBftxEXkTPKmyWz2ATDKJni1KAeTqRNmzpzj8E4Yffp8DLO5IsxmqQ9tACAQQKXYDGUnhIX1T1GXAMnoSkspnW4MDAbZPew1GAwSJFfGiy9mxerVK5zWszZbtizQ688nMeIcAgOzOOVYiMg7yZbT8nrv67sIOl1e+PjITov5VTeCWrUq4Mcfl7pVH29yDx4RwJ4/f15lvAoWLKh60BYuXBjDhw9XmTBbR44cQa1atdRis+DgYIwfPz7Bfcnq7uLFi6sxISEhWLNmjRN/EnoWp06dgk5XHkDmREbUUQuiHNle5eTJkzhwYC/M5v5yoj3erfICOkQtOAgPD09xDapsxRwW1hXNmunx9tsvYtmyZWr3q9y5pUOAc7Rr1wZm8y8A7C0auw6DYSE6dmzjtOMhzyBvGqWlojSely2J165dy5aKlCJSpnj16mVMnjwenTtXxEcfvY2dO3diy5aNyJw5sb8Bnkc2hXR0BwLNXjWaF0jZihQnBjLy4vjFF1+gSJEiajVit27dVIufiRMnqjESxDRs2FC195o9ezaOHj2Kd999Vz3xu3eX9hxQvwyyreiYMWNUjaG05WjRogUOHDiA0qVLu/inpPhkgZZOJwuL5LfT3rvvq9DrDepNjaM8aQsXv52LRVHo9X64fPlyih+raNGiqiesK7Vs2RIhIeVx4kRTmEzTAbSIfVnYAoOhNwID0+ODDz5w6TGSezl9+rTaavjMmZPw8ZEzCDp1RqFw4WLq7IEkCIieR5YsWdCnj5SNpV0sIfCyDGzjxo3V6QUJUGVHn+bNm+Pjjz+O0/FANlWQjKzsDFaqVCm0bt0aH330ESZPnmwdI7uHyX3169cPJUqUwKhRo1ChQgVMny5/uMndvPXWW4iOPqsWbiUUBYNhDpo1a+7QFfuyhWGM44mMOAuz+aHNOM8mi7k2b16P2rXLAngben1mGAyS7aiPYsUM+OWXrWnmZ6WUu337NmrXroez8muJXxEVdRFRURcA7ML580Z1m+xjT0SU2jwiA2vP3bt31bs1i127dqmFL0/aFMXU1UiGS150AwMD1Zi+ffvGuR8ZIzuLJUZ6jtr2HbWcrpb+t0n1wLXcxj65z6969eqoW7c+fvutC0ymGbHtnmTR0QXodINgMPyJAQO+SjDHKZl7yYpWqlQVJ05Mg9lc386vyERkyJBLLThIK/+3cpZi3bpwtXHB5s2bYTKZVPeFatWqqbqzZ/k5+bx3rdSef0kkSOcPX98dAPJIh/DYWyoCWIsHD8qoJEJaz6LZw+e++8+9O/zfMAPrODpNml16mN9//x0VK1ZU5QNSSiAkOys1slJmYCF/kCUbKx8l4yrB7bfffqvKCGzbNI0cOVI1U7ZHVrjL7fFJ+cGT/nVERETkziIjI9G2bVuVAMuUKZNTH1uSX9JqdPHiu/D3d+xjR0beQ+vWAS75ubw2Aztw4MCn1gDKohrbmiqpPZQyAFnJbQleU5MsULDN2sqTUBaIScCc1BNF3ult3LgRDRo0UO2KKGWkTnn16tVq84ySJUuqnoCJvYFwxNxLJlK6EVy8qM6VKlmy5MDw4UPUhh5kH5/3aXv+y5WrjHPnZMekMYmM+C+Cg8Nx7NgBeBs+991/7h254Pd5MQObRgLYsLCwpwYDUvNqu8BGGsrLqeU5c6Tp8RNSpxc/i2r52lLDl9iYpGr8pL7SXo2l/JIk50UqueMoaVWqVFGXZ5GSuZc3SSdONFQ9YS07cckCQdsSFfLM572cdNqzZ496QyTlQeXLl1eL2VJ797O0MP+FC+dHRMQWmExGuwsrDYYtaoy7/t97+3Pf2+ee/y9pi0sD2OzZs6tLckjmVYJXKR2QOiy9Pu76M6nXGzJkiHonZnmSyjuyYsWKqfpXy5iYzNqT+iwZI9cTxSfPsdq1a7v6MMiB/vrrL7z5Zivs2LEdRmMO6HQZERU1ER999B98//0ivPKKbKlLienRoztWrWoKYFHsrm22lsBk+g09eqx00dERuT9mYL2sC4EEr3Xq1EG+fPlU3av8EZJdmuRiIXUtkh2TfrHHjx/HkiVLVNcB29P/vXv3xrp169TuTdKaS+pbZa/7Xr16uegnIyJHkjew0kJPekLbLr4UsjitcePXsHv3SQA/qe0po6J+l0Z9uH27LF599TXVoo8SJ4sX27fvAJ2uAwA5e7YWwDoA70Kna4vWrdum+jbIREQeE8BKllQWbkn2NG/evKrRu+ViIcXRGzZswLlz51SWVsoThg0bZu0BK6T0QBZfSflB2bJl8cMPP6gOBOwBS+TZJDgdPXo0goLyo0yZMur3O2fOIAwdOtS64Ym8eZVNKqKjlwJoZvPyVwxm80qYTDkxfvwEl/4c7k66UsybNxcTJ05AUNA2AK9KWIs8eTZj3Lix+O67/yU4O0ZETzh6EwNzKmR0PYVHtNGSOtnkLJyRP1xSs5gUWfwlFyJKG6SmtX37juqsi6bJwk7pMmLA3bvLMGbMROzffxCrVq1Uu/AZjSGIjq5l5178EB3dBd9/PxrffjuP21YmwWAwqDNbckZL6sNl/gsUKKCuJyJyFo8IYImIEiNnXhYvXhhbl9na5pYaMJsbY926Rvj+++9V/1KTSXqXJhacBuHRo4cqm2s08qXxaSRgtV1kS0RPxxpYx+G5HiLyaF988SUMBtlJLNTOrQ2h17+C2bO/VAs69fq90jUxkXvahvz5CzN4JaJUwxICx2EAS0Qe7fTpP2AyVU80s2o2V8eZM3+ga9euMJvvAPjMzqi90OsX4/33n9TMExGR+2IAS0QeLWvWQOh0F5MYcQGBgZnV6W5Z6AWMhk4nraCWyZYVAPpCr6+LSpVewocffujEIycib8MMrOMwgCUij9a2bWhsO6cIO7dehl6/FB06tLHu/rdo0SKULHkZwFsA6iMg4H/o1+9DbN68AX5+fk4/fiIienYMYInIo7Vv3x4FCxaB0dgQwBrJcUhvAgBbYTDUR44c2eJsO926dWscPXoQf/75p2rPd/36ZYwdOxbp06d36c9BRGkfM7COw9UKROTRJPDctm0T3nzzbezb1xQGQ2bodHpER99CsWJlsHz5ZrUVsC1pkxUUFAR3JR0TFi9ejIiICGTMmFFtdct+1URETzCAJSKPFxwcjL17d2L37t3YsmWL6k1as2ZNtRWwp/V0lcC1S5fuePjwAYzGIjCb/1K7Br7xRkvMn/8tM8VEHkzTHJ8x1eSEkxdiAEtEaYIEqtWqVVMXT7Vp0ya0bdtOtQTTtHGIigoGIDuJLcZPP72PDh064ccff3D1YRIRuRwDWCKiZyDZ3e3bt6utrWXTgxo1aqBx48YO2YlqxIhR0Okqw2yer3YTi+ELoCNMJh2WL++Io0ePIiQkJMWPRUTOx40MHIcBLBFRMsnWqc2bv4kjRw7AaMwJnc4HY8aMQYECRbBy5TK1nfXzunHjBn799WcA/7MJXm2FwmDogx9++IEBLJGHYgDrOOxCQESUDA8ePECdOvVx4sRt1T82OvoqoqIuqU0QLl3KoG67evXqc9//P//8E/uZbHdrjy/0+hxqgRcRkbdjAEtElAwLFizAhQtnER29DsArNjt/VYLJtAH37v2LWbNmPff9586dG35+GQBsT2TEn4iOPqO2xCUiz8Q2Wo7DAJaIKBkWLfoegPSafdHOrdlhMrXGggUy5vnIJgqdO3eEwTADwO/xbpW/UIPUmDZtYjZlICLyZgxgiYiS4fbtu9C0vEmMyIu7d++m6DFGjhyJ/PmzwWCoAmAYgC2S+4Ve/zJ0ugWYM2c2MmXKlKLHICLXYQbWcRjAEhElQ6lSxWA0/hK7y1dCev3PKFEiZaf3ZcOFPXt+RffuofDzmwKgnuw1hqpV9Vi3bh3atZMWW0RExC4ERORykZGRanX96dOnERAQoHaeKlSoENzJe+91x8KFCwB8DaBrvFvXwWzejB49vkvx40gQO3PmTEycOBFXrlxRO3HlzJkzxfdLRK7HLgSOwwCWiFxq6dKl6Nr1Pdy7dwc+PnlhNt/CgAEDVNN+OWWeLl06uINatWqhW7fu+PLL7gCk3VXb2B6ty6DTfYlXX22G0NBQhz2ev78/ihQp4rD7IyJKS1hCQEQu3XkqNLQ17t9voBYuRUVdhMl0A5o2A999twjdur0Hd9rpa/bsWZg2bSqCg3cAaKJO8WfL9iNGjBiK5cuXwWhkToCIEscaWMfhqy0Ruczw4Z9Ap6sCs3mhTfN+fwA9YTbrMX9+DwwbNtRtMpF6vR4fffQRevXqhXPnzsFsNqNAgQLw8fFx9aERkQdgCYHjMANLRC4hTf937vwFZnOvRHaekpZSGVWJgbuRQLZw4cIoWrQog1ciIhdgBpaIXOLOnTuxnwUnMsJP7TyV0tZURETughlYx2EGlohcIm/evPD1fQHAr0nsPHXObcoHiIjIfTCAJSKXkPZQbdu2gdE4TQWrcUlKYSj8/NI7dGU/EZEraZrjF3Bp9ltTp3kMYInIZT79dBRy5PCF0VgZwHgAuwH8AL2+PnS6/2HWrOkq0CUiIrLFAJaIsHnzZrRo8Sby5MmPAgWKonfv3jhz5kyqP25QUBD27t2J1q3rw8dHtk6tBqAVypV7gFWrVqFjx46pfgxERM7CNlqOwwCWyItpmoaBAweifv36CA//HVevtseFC00wc+YihISUVduXOiOInT//f7hx4xqOHDmC8+fPY//+PWjatGmqPzYREXkmdiEg8mIrV67EuHHjAExGdHQfadevro+OHgeTKRRvvvkWLl48r7Y3TW2ZM2dWFyKitIpdCByHGVgiLzZlyucwGGoC+I81eI3hB037Bo8eRWPu3LkuPEIiorSDJQSOwwCWyIvt2vUrTKa3Erk1G8zmOtixQ7ZNJSIich8sISDyYjqdvIeNTuL2aOj1fk49JiKitIolBI7DDCyRF6tbty4MhkWynMvOrVcAbEO9evVccGSew2QyqVripk2boXjxENSqVQdz5sxBZGSkqw+NiCjNYgBL5MXCwv4Dk2k/gCHxMrG3oNe3QaZMAejQoYMLj9C9PX78WLUfa9GiBdav/wsREXXw66/p0aNHT5QvXwnXrl1z9SESkRthDazjMIAl8mINGjTAxIkTAYyB0VgAQDcAraHX50X69EewZs0qBAQEuPow3dbw4cOxZo20GlsFk0k2Yfg/aFo4NO0Izp69g9at27n6EImI0iQGsEReLiwsDAcPHkTnzq8iJOQAXnrpAkaOHIwzZ06hevXqrj48tyUlAjNmzIbZ3BvAa/FuLYXo6KnYvn2L6m1LRCSYgXUcLuIiIpQrV07VbVLyHT58GPfv31EZa/taQK9Ph61bt6JMmTJOPjoiorSNASwR0XPuYpb0iSzpq6u3GUdE3o5dCByHASwR0XOQrKq/f0ZERi6VHLadEeEwmx+iVq1aLjg6InJHDGAdhzWwRETPIUOGDOjevQv0+ikAtsa79SyMxj6oXLk6Klas6KIjJCJKu5iBJSJ6TqNHj8bhw8ewdesr0OvrwWyuDJ3uDwDLkTdvfixdKj12iYhiSEWRozOmmpdWKTEDS0T0nPz8/LB+/RrMnz8fNWqYEBS0CGXK/IFJk8bh0KF9yJcvn6sPkYgoTWIGlogoBXx8fNC+fXt1ISJKCmtgHYcZWCIiIiLyKMzAEhERETkBM7COwwwsEREREXkUZmCJiIiInIAZWMdhAEtERETkBAxgHYclBERERETkUZiBJSIiInICZmC9OAP76NEjlCtXDjqdDocOHYpz25EjR9S+4y+88AKCg4Mxfvz4BN+/dOlSFC9eXI0JCQnBmjVrnHj0REREROR1AWz//v2RJ0+eBNffu3cPDRs2RP78+bF//35MmDABI0aMwJw5c6xjdu7ciTZt2qBLly44ePAgWrRooS7Hjh1z8k9BRERE3pqBdfTFG3lUALt27Vps2LABEydOTHDbggUL8PjxY3zzzTcoVaoUWrdujY8++giTJ0+2jpk2bRoaN26Mfv36oUSJEhg1ahQqVKiA6dOnO/knISLyDmazGXv27MH69evx+++/u/pwiCiN8Jga2OvXr6Nbt25YsWIF/P39E9y+a9cuvPzyy/D19bVe16hRI4wbNw63b99GYGCgGtO3b9843ydj5D6TKlmQi22mV0RFRalLYiy3JTWGUgfn3nU4967lbvO/ZMkSfPLJZ/jzzwvW66pXr4VJk8ajZMmSSEvcbe69SXLn3h3+b1gD62UBrKZpeOedd9CjRw+89NJLOH/+fIIx165dQ8GCBeNclzNnTuttEsDKR8t1tmPk+sSMGTMGI0eOTHC9ZILtBdLxbdy48aljKHVw7l2Hc+9a7jL/GTNmxIQJYxNcL6/h9l7H0wJ3mXtv9LS5j4yMdNqxUBoPYAcOHKgypEk5efKkChbv37+PQYMGwdnkMW2ztpKBlQViUm+bKVOmJN/pyS9TgwYN4OPj46SjJcG5dx3OvWu5y/zL63XRosXw8OHbAKYA0NneCoOhDho0eBFLlixCWuEuc++Nkjv3ljOorsQMbBoJYMPCwlRmNSmFChXCli1b1On/dOnSxblNsrHt2rXDt99+i1y5cqkyA1uWr+U2y0d7Yyy32yOPGf9xhfySJOdFKrnjyPE4967Duffu+V++fDlu374FTZOkw5OyrhhZAPTCihU9cevWrQRnxTydq+femz1t7t3h/0XTHB9wahq8kksD2OzZs6vL03z++ef49NNPrV9fuXJF1a5KfVWVKlXUddWqVcOQIUPUOzHLk1TekRUrVkyVD1jGbN68GX369LHel4yR64mIyDEuXboEozEXoqLyJjKiAjTNjMuXL6e5AJaInMMjamDz5csX5+sMGTKoj4ULF0bevDEvkG3btlW1qtIia8CAAao1lnQdmDJFTl/F6N27N2rXro1JkyahadOmWLx4Mfbt2xen1RYREaWMJCZMppsAbsVmXOM7bR1H5E1YQuClbbSSEhAQoGplz507h4oVK6ryhGHDhqF79+7WMdWrV8fChQtVwFq2bFn88MMPqgNB6dKlXXrsRERpSatWraDXy3nNz+3cGgW9fipq1qyt1hMQkWvMmDEDBQoUUBs7ydnsvXv3Jjr2yy+/VBtFyRltudSvXz/J8c7gERnY+GTCpTNBfGXKlMEvv/zy1BdWuRARUerIkSMH+vfvh9GjpYPLQzn/BSA3gN+g0/0Xev0hjB692dWHSeS1GdglS5aoBeqzZ89WwevUqVNVaWZERIT6/Y1v27ZtaiMoSQRKwCsL8GUx+/HjxxEUFARXSDMZWCIich+yUYzshvjCCzMABEGnk8VcVZAnTwRWr16lsjlE5BqTJ09WvfU7d+6sejJLICutQWUzKHtks6j3338f5cqVQ/HixfHVV1+pTUpkXZGreGQGloiI3Jter8fw4cPVotnw8HDcuXNHrVuQU48Gg8HVh0eU5jKw9+K1CUusi5LsWrp///44rUnl91V+N6XjU3JIT11ZNJ8li70ad+dgAEtERKm6PkEW2RJR6gqOV1MubyDlLEh8N2/ehMlksrux06lTp5L1WLJYPk+ePCrodRUGsEREREQenoG9dOlSnA2W7GVfHWHs2LGqi5PUxUo9rKswgCUiIiLy8AA2U6ZMSe4QapEtWzZVxvOsGzuJiRMnqgB206ZNauG8K3ERFxEREZGX8PX1Ve1GbRdgWRZkJbWx0/jx49XizHXr1qmdUF2NGVgiIiIiL2qj1bdvX3Tq1EkFopUrV1ZttB48eKC6EoiOHTuq9lhjxoxRX0vbLOmtL730pZXptWvXrBtLWTaXcjYGsEREREReJDQ0FH/99ZcKSiUYlfZYklm1LOy6ePGi6kxgMWvWLNW94K233krWQjFnYABLRERE5EUZWNGrVy91sUcWaNk6f/483A1rYImIiIjIozADS0RERORlGVhPxwCWiDyKrJaV3WKuXLmiWr7UqFEjTq0WERGlfQxgichjrF69Gh991Bfnzp2xXpcvXyFMmTIBb775pkuPjYjoaTTN8RlTTYNXYtqCiDzCqlWr0Lz56zh/vhCA7QBuA/gVFy+WVCtjly1b5upDJCIiJ2EAS0QeUTbQu3cYgAbQtHAALwPIDKA6gJXQtOYqMyv7exMRuXsNrKMv3ogBLBG5Pal5lbIBTRsMwGDnZWwwrly5mKD1CxGRO2EA6zgMYInI7cmCrRiJ7b1dJt44IiJKy7iIi4jcnnQbiHEcQA07I47HG0dE5H7YRstxmIElIrcnrbKk24BON1ZeruPdKktwxyJXrryoW7eui46QiIiciQEsEbk96fM6depEALKA6w0AewE8BLAfQCsAP6hWWkYjTyoRkftiDazjMIAlIo/wxhtvYOnSpQgKOgygCgB/AC8hV649WLRoEVq3bu3qQyQiIidhuoKIPEbLli3RokUL1W3AshOXlA0w80pEnoA1sI7DV30i8igGgwH16tVz9WEQEZELMYAlIiIicgJmYB2HASwRERGREzCAdRwu4iIiIiIij8IMLBEREZETMAPrOMzAEhEREZFHYQaWiIiIyAk0zfEZU002I/RCzMASERERkUdhBpaIiIjICVgD6zjMwBIRERGRR2EGloiIiMgJmIF1HAawRERERE7AANZxWEJARERERB6FGVgiIiIiJ2AG1nGYgSUiIiIij8IMLBEREZETMAPrOMzAEhEREZFHYQaWiIiIyAmYgXUcZmCJiIiIyKMwA0tERETkBMzAOg4DWCIiIiInYADrOCwhICIiIiKPwgwsERERkRNomuMzppoGr8QMLBERERF5FGZgiYiIiJxAsq86nePv0xsxA0tEREREHoUZWCIiIiInYAbWSzOw4eHhqFKlCvz8/BAYGIgWLVrEuf3ixYto2rQp/P39kSNHDvTr1w/R0dFxxmzbtg0VKlRAunTpUKRIEcybN8/JPwUREREReUUGdtmyZejWrRtGjx6NV155RQWmx44ds95uMplU8JorVy7s3LkTV69eRceOHeHj46O+R5w7d06N6dGjBxYsWIDNmzeja9euyJ07Nxo1auTCn46IiIjSOmZgvSyAlWC1d+/emDBhArp06WK9vmTJktbPN2zYgBMnTmDTpk3ImTMnypUrh1GjRmHAgAEYMWIEfH19MXv2bBQsWBCTJk1S31OiRAns2LEDU6ZMSTSAffTokbpY3Lt3T32MiopSl8RYbktqDKUOzr3rcO5di/PvOpx79597d/i/YQDrZQHsgQMHcPnyZej1epQvXx7Xrl1TAaoEtKVLl1Zjdu3ahZCQEBW8WkhQ2rNnTxw/flx9n4ypX79+nPuWMX369En0sceMGYORI0cmuF4CZilVeJqNGzc+409LjsK5dx3OvWtx/l2Hc+++cx8ZGem0Y6HU5xEB7NmzZ9VHyaROnjwZBQoUUFnUOnXq4PTp08iSJYsKam2DV2H5Wm6zfLQ3RrKqDx8+VLW18Q0aNAh9+/a1fi1jg4OD0bBhQ2TKlCnJd3ryy9SgQQNVxkDOw7l3Hc69a3H+XYdz7/5zbzmD6krMwKaRAHbgwIEYN25ckmNOnjwJc+z/zpAhQ9CyZUv1+dy5c5E3b14sXboU7733Xqodoyz2kkt88kuSnBep5I4jx+Pcuw7n3rU4/67DuXffuef/S9ri0gA2LCwM77zzTpJjChUqpBZkxa95laBSbpPOA0IWb+3duzfO916/ft16m+Wj5TrbMZJJtZd9JSIiInIUZmDTSACbPXt2dXmaihUrqoA1IiICNWvWtJ4yOH/+PPLnz6++rlatGj777DPcuHFDtdASckpBglNL4Ctj1qxZE+e+ZYxcT0RERESewSP6wEoQKq2vhg8frhZPSSAri7NEq1at1EepSZVAtUOHDjh8+DDWr1+PoUOH4oMPPrCWAMh9SD1t//79cerUKcycORPff/89/vOf/7j05yMiIqK0T7KlqXHxRh6xiEtIxwGj0agCVFlwJRsabNmyRW1oIAwGA1avXq0CW8mopk+fHp06dcInn3xivQ9poSWbIUjAOm3aNFVD+9VXX7EHLBEREZEH8ZgAVoqvJ06cqC6JkXKC+CUC8UnngoMHD6bCERIREREljjWwXhjAEhEREXkyTXN8wKlp8EoeUQNLRERERGTBDCwRERGRE6TG6X6zl5YQMANLRERERB6FGVgiIiIiJ2AG1nGYgSUiIiIij8IMLBEREZETMAPrOMzAEhEREZFHYQaWiIjc3v379/H3338ja9asyJgxo6sPh+i5MAPrOMzAEhGR2zp58iTefjsUgYFZ1HbgWbJkRWhoa0RERLj60IieK9hMjYs3YgBLRERu6fDhw6hcuRqWL98Hk2k8gA2Ijh6LH3/ci0qVquLIkSOuPkQichGWEBARkVvq3LkbHj4sCJNpO4BMsdc2QHR0F0RGvoyuXd/D3r27XHyURMnHEgLHYQaWiIjczsGDB3Hw4G8wmUbZBK8WATCZRuK333arLC0ReR8GsERE5HZOnToV+1mdREbUjTeOyP2xBtZxGMASEZHbyZAhQ+xn1xIZcTXeOCLyJgxgiYjI7bzyyivIkCEAwMxERsxCpkyBqFs3JhNL5AmYgXUcBrBEROR20qdPj4ED+wGYAmCMdIKNvUU+fgbgcwwa1B/+/v4uPlIicgV2ISAiIrc0ePBg3L17FxMnDoVePxp6fX6YzedhNj9E//4DMGDAAFcfItEz0TTHZ0w1DV6JASwREbklnU6H8ePH48MPP8R3332Hq1evIk+ePGjfvj3y5s3r6sMjemYSvOp0jr1PjQEsERGR+wkODsagQYNcfRhE5EYYwBIRERE5ATOwjsNFXERERETkUZiBJSIiInICZmAdhxlYIiIiIvIozMASEREROQEzsI7DDCwREREReRRmYImIiIicgBlYx2EGloiIiIg8CjOwRERERE7ADKzjMIAlIiIicgIGsI7DEgIiIiIi8igMYImIiIiclIFNjcvzmDFjBgoUKIAXXngBVapUwd69e5Mcv3TpUhQvXlyNDwkJwZo1a+BKDGCJiIiIvMiSJUvQt29fDB8+HAcOHEDZsmXRqFEj3Lhxw+74nTt3ok2bNujSpQsOHjyIFi1aqMuxY8fgKgxgiYiIiJxA6lUdnX3VnqMGdvLkyejWrRs6d+6MkiVLYvbs2fD398c333xjd/y0adPQuHFj9OvXDyVKlMCoUaNQoUIFTJ8+Ha7CRVzPSIt9pty7dy/JcVFRUYiMjFTjfHx8nHR0JDj3rsO5dy3Ov+tw7t1/7i1/ty1/x13jXqrd5714cUm6dOnUJb7Hjx9j//79GDRokPU6vV6P+vXrY9euXXYfQa6XjK0tydiuWLECrsIA9hndv39ffQwODnb1oRAREdFz/B0PCAhw6mP6+voiV65cuHYtdWKHDBkyJIhLpDxgxIgRCcbevHkTJpMJOXPmjHO9fH3q1Cm793/t2jW74+V6V2EA+4zy5MmDS5cuIWPGjNAl0QtD3gnJk0nGZsqUyanH6O04967DuXctzr/rcO7df+4l8yrBq/wddzZZ+HTu3DmV/UwNmqYliEnsZV/TEgawz0jS7Hnz5k32ePll4ouZa3DuXYdz71qcf9fh3Lv33Ds78xo/iJWLq2XLlg0GgwHXr1+Pc718LVlie+T6ZxnvDFzERUREROQlfH19UbFiRWzevNl6ndlsVl9Xq1bN7vfI9bbjxcaNGxMd7wzMwBIRERF5kb59+6JTp0546aWXULlyZUydOhUPHjxQXQlEx44dERQUhDFjxqive/fujdq1a2PSpElo2rQpFi9ejH379mHOnDku+xkYwKYSqT2RAuq0XoPijjj3rsO5dy3Ov+tw7l2Hc//sQkND8ddff2HYsGFqIVa5cuWwbt0660KtixcvqpJJi+rVq2PhwoUYOnQoBg8ejKJFi6oOBKVLl4ar6DTX9pMgIiIiInomrIElIiIiIo/CAJaIiIiIPAoDWCIiIiLyKAxgiYiIiMijMIB9Rp999plajefv74/MmTPbHSOr96TNhIzJkSMH+vXrh+jo6Dhjtm3bhgoVKqhVk0WKFMG8efMS3M+MGTNQoEAB1fi4SpUq2Lt3b6r9XGkJ5y1lfv75ZzRr1kztViM7u8Tf61rWfcrK1dy5c8PPz0/tn33mzJk4Y27duoV27dqppuLye9KlSxf8888/ccYcOXIEtWrVUv9PsovO+PHj4e2kZU2lSpXUTn/y2tGiRQtERETEGfPvv//igw8+QNasWdX2kS1btkzQYNxRr0HeZtasWShTpoy1Ib70uFy7dq31ds6984wdO1a9/vTp08d6Heef4pAuBJR8w4YN0yZPnqz17dtXCwgISHB7dHS0Vrp0aa1+/frawYMHtTVr1mjZsmXTBg0aZB1z9uxZzd/fX93HiRMntP/7v//TDAaDtm7dOuuYxYsXa76+vto333yjHT9+XOvWrZuWOXNm7fr16077WT0R5y3l5Dk7ZMgQ7ccff5QOJdry5cvj3D527Fj13F+xYoV2+PBhrXnz5lrBggW1hw8fWsc0btxYK1u2rLZ7927tl19+0YoUKaK1adPGevvdu3e1nDlzau3atdOOHTumLVq0SPPz89O++OILzZs1atRImzt3rpqTQ4cOaa+++qqWL18+7Z9//rGO6dGjhxYcHKxt3rxZ27dvn1a1alWtevXqDn8N8kY//fSTFh4erp0+fVqLiIjQBg8erPn4+Kj/D8G5d469e/dqBQoU0MqUKaP17t3bej3nn2wxgH1O8kfGXgArvzB6vV67du2a9bpZs2ZpmTJl0h49eqS+7t+/v1aqVKk43xcaGqr+eFlUrlxZ++CDD6xfm0wmLU+ePNqYMWNS6SdKGzhvjhU/gDWbzVquXLm0CRMmWK+7c+eOli5dOhWECvmjIN/322+/WcesXbtW0+l02uXLl9XXM2fO1AIDA62/E2LAgAFasWLFnPSTeYYbN26oudy+fbt1riWgWrp0qXXMyZMn1Zhdu3Y59DWIYsjz9KuvvuLcO8n9+/e1okWLahs3btRq165tDWA5/xQfSwgcbNeuXQgJCbE2AxaNGjXCvXv3cPz4cesYOe1qS8bI9eLx48fYv39/nDHSUFi+toyhhDhvqe/cuXOq6bXtHMve4lKqYZlj+ShlA7LDi4WMl/+LPXv2WMe8/PLLaktD298BOV1++/Ztp/5M7uzu3bvqY5YsWdRHeX5HRUXFmf/ixYsjX758ceY/pa9BBJhMJrXbkOxOJKUEnHvnkBIBKQGIP0ecf4qPO3E5mPxxt/3lEZav5bakxsgv2cOHD9UfcHnxtDfm1KlTqf4zeKqbN29y3lKZ5Tlsb45tn99Se2bLaDSqIMx2TMGCBRPch+W2wMBAeDvZm1zq/2rUqGHd7UbmRoL++PX38ec/pa9BUtvsrY4ePaoCVqm3lDrL5cuXo2TJkjh06BDnPpXJG4YDBw7gt99+S3Abn/sUHzOwAAYOHKiKxZO6MAAiImdnoo4dO6b+qJPzFCtWTAWrcragZ8+ear/4EydOuPqw0rxLly6hd+/eWLBggVrYSfQ0zMACCAsLwzvvvJPkmEKFCiXrvnLlypVg1btllaTcZvkYf+WkfC2rXuXdn8FgUBd7Yyz3QQlly5aN85bKLPMocypdCCzka9lL2zLmxo0bcb5PVgFLZ4Kn/Q7YPoY369WrF1avXq06QuTNm9d6vcyNlMrcuXMnTibK9jnuiNcgbyZZPlmZLipWrKiygdOmTVN7x3PuU4+UCMjrhnQHsJAzavI7MH36dKxfv57zT3EwAwsge/bsqpYmqYttrV5S5NSTnIKy/QO+ceNG9cshp6EsYzZv3hzn+2SMXC/kseSF03aMnE6Ury1jKCHOW+qT0/7yB8B2juXUm2SrLHMsH+WPjPxBstiyZYv6v5BaWcsY+cMkNW22vwOS/fLm8gFZNyfBq5y2ljmLX2Yhz28fH5848y91w9I6yHb+U/oaRE/I8/bRo0ec+1RWr149NXeS/bZcpI5e2vFZPuf8UxwJlnVRki5cuKDac4wcOVLLkCGD+lwusnLSto1Hw4YNVRscac2RPXt2u208+vXrp1ZRzpgxw24bLVnZPW/ePLWqu3v37qodlO3qSkqI85Zy8ly2PK/lJULaxsnn8ty3tNGSOV25cqV25MgR7fXXX7fbRqt8+fLanj17tB07dqhVxbZttGRFsbTR6tChg2pRJP9v8jvh7W20evbsqbqbbNu2Tbt69ar1EhkZGaeVkLTW2rJli2olVK1aNXWxcNRrkDcaOHCg6vhw7tw59dyWr6V7xoYNG9TtnHvnsu1CIDj/ZIsB7DPq1KmT+qMe/7J161brmPPnz2tNmjRRfS2lB11YWJgWFRUV535kfLly5VTP0kKFCqm2XPFJfzr5ZZUx0h5KemrS03HeUkaem/ae4/Lct7TS+u9//6sCUHmzUK9ePdUz09bff/+tAlZ5kyctbDp37mx9k2chPWRr1qyp7iMoKEgFxt7O3rzLxfb1Qd4ovP/++6q9k/whfuONN1SQa8tRr0He5t1339Xy58+v5kQCH3luW4JXwbl3bQDL+SdbOvknbk6WiIiIiMh9sQaWiIiIiDwKA1giIiIi8igMYImIiIjIozCAJSIiIiKPwgCWiIiIiDwKA1giIiIi8igMYImIiIjIozCAJSIiIiKPwgCWiIiIiDwKA1gisuudd96BTqdTF19fXxQpUgSffPIJoqOjrWNkI785c+agSpUqyJAhAzJnzoyXXnoJU6dORWRkZJz7+/PPP9X9lC5dOtnHcO3aNXz44YcoVKgQ0qVLh+DgYDRr1gybN2926M+aFv6vWrRo8dRxP//8s5q/PHnyqP/XFStWOOX4iIgcjQEsESWqcePGuHr1Ks6cOYOwsDCMGDECEyZMsN7eoUMH9OnTB6+//jq2bt2KQ4cO4b///S9WrlyJDRs2xLmvefPm4e2338a9e/ewZ8+epz72+fPnUbFiRWzZskU95tGjR7Fu3TrUrVsXH3zwQar8vGndgwcPULZsWcyYMcPVh0JElDIaEZEdnTp10l5//fU41zVo0ECrWrWq+nzJkiWavISsWLEiwfeazWbtzp07cb4uVKiQtm7dOm3AgAFat27dnvr4TZo00YKCgrR//vknwW23b9+2fn7hwgWtefPmWvr06bWMGTNqrVq10q5du2a9ffjw4VrZsmW1r7/+WgsODlbjevbsqUVHR2vjxo3TcubMqWXPnl379NNP4zyG/GwzZ87UGjdurL3wwgtawYIFtaVLl8YZc+TIEa1u3brq9ixZsqif6/79+wnmcMKECVquXLnUmPfff197/Pixdcy///6rhYWFaXny5NH8/f21ypUra1u3brXePnfuXC0gIEDNXfHixdXxN2rUSLty5Yr155Njtb3Yfn9iZNzy5cufOo6IyB0xA0tEyebn54fHjx+rzxcsWIBixYqp7Gt8cno6ICDA+rVkZ6WkoH79+mjfvj0WL16ssoGJuXXrlsq2SqY1ffr0CW6XUgVhNpvV48v47du3Y+PGjTh79ixCQ0PjjP/jjz+wdu1adZ+LFi3C119/jaZNm6qyBvm+cePGYejQoQkyw5JNbtmyJQ4fPox27dqhdevWOHnypLpNjr9Ro0YIDAzEb7/9hqVLl2LTpk3o1atXnPuQn10eXz5+++23KhMtFwsZv2vXLjUnR44cQatWrVTmW7LeFjJ3EydOxPz581UZwMWLF/Hxxx+r2+SjZLYt2XK5VK9ePdG5JSJKE1wdQROR+2dgJYO6ceNGLV26dNrHH3+sritRooTKfCZH27ZttT59+li/loyoZBYTs2fPHpUh/PHHH5O83w0bNmgGg0G7ePGi9brjx4+r7927d681QymZzXv37lnHSAazQIECmslksl5XrFgxbcyYMdav5T569OgR5/GqVKmisrdizpw5WmBgYJwMcXh4uKbX660ZYJnD/Pnzq2yvhWSIQ0NDrdljOf7Lly/HeZx69eppgwYNUp/LPMmx/P7779bbZ8yYoTLHSWXLn4YZWCLyZEZXB9BE5L5Wr16tFmdFRUWpbGfbtm1VHayIiYGe7s6dO/jxxx+xY8cO63WShZUsqCw+sie59y3ZUFnYJReLkiVLqgyt3FapUiV1XYECBZAxY0brmJw5c8JgMECv18e57saNG3Huv1q1agm+ljpfy2NLPalthrhGjRpqniIiItT9iVKlSqnHssidO7eq5xXy0WQy4cUXX4zzOI8ePULWrFmtX/v7+6Nw4cJx7iP+sRIReRMGsESUKFkwNWvWLNU9QFauG41PXjIk6Dp16tRT72PhwoX4999/VacC2wBVAr3Tp08nCN5E0aJFVRlCcu4/OXx8fOJ8Lfdt7zo5JkdL6nH++ecfFdzu378/TpAr5I1DUveR3CCfiCgtYg0sESVKsovSPitfvnxxglch2VgJQKXjQHwSXN29e1d9LplW6WAgmUvLRWpKa9WqhW+++cbu42bJkkXVl8pqeXu1spLVFSVKlMClS5fUxeLEiRPqdsnEptTu3bsTfC2PaXls+Tlsj+/XX39VWV2pDU6O8uXLqwysZFNlnm0vuXLlSvZxyhsMuR8iIm/BAJaInossHJLFUm3atMHo0aOxb98+XLhwQZUdyGItS1utAwcOoGvXrqr/q+1Fvk8WNdn2lbUlwasEZZUrV8ayZcvUoiY5bf/5559bT+3L44SEhKgFVvI4e/fuRceOHVG7dm3VjzalZGGWBNkSqA8fPlzdv2WRljzmCy+8gE6dOuHYsWPq55WetdJazFI+8DSSfZb7kWOWMotz586pxxgzZgzCw8OTfZxSIiELwKR04ebNm6rkwx7J+FreRAh5PPlcFoUREXkSBrBE9FzkNLaUB0yePFk1xJegsUyZMqpGVjoDSAZVsq+SCS1evHiC73/jjTdU5nHNmjV27182L5CgVMoYJIMrQW+DBg3UJgZS1mA5BskASyeAl19+WQW08n1LlixxyM84cuRI1R1Afq7//e9/qoOBJbMrdanr169XHRCk1vatt95CvXr1MH369Gd6jLlz56oAVn5GydzKhgTS1UCy3snVrVs39b0StGfPnl1lgu2RNxmS9ZWL6Nu3r/p82LBhz3TMRESuppOVXK4+CCIidyPB8fLly5O1wxURETkXM7BERERE5FEYwBIRERGRR2EbLSIiO1hdRUTkvpiBJSIiIiKPwgCWiIiIiDwKA1giIiIi8igMYImIiIjIozCAJSIiIiKPwgCWiIiIiDwKA1giIiIi8igMYImIiIgInuT/Af8nUxGKcTX+AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize the PCA-reduced data\n", "plt.figure(figsize=(8, 6))\n", "plt.scatter(X_2d[:, 0], X_2d[:, 1], c=y, cmap='bwr', edgecolors='k')\n", "plt.title(\"Breast Cancer Dataset (PCA-reduced to 2D)\")\n", "plt.xlabel(\"PCA Component 1\")\n", "plt.ylabel(\"PCA Component 2\")\n", "plt.colorbar(label=\"Target\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "id": "bd664de8", "metadata": {}, "outputs": [], "source": ["# Train/test split\n", "X_train, X_test, y_train, y_test = train_test_split(X_2d, y, test_size=0.25, random_state=42)"]}, {"cell_type": "code", "execution_count": 6, "id": "6c696614", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>SVC(kernel=&#x27;linear&#x27;)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>SVC</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.svm.SVC.html\">?<span>Documentation for SVC</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>SVC(kernel=&#x27;linear&#x27;)</pre></div> </div></div></div></div>"], "text/plain": ["SVC(kernel='linear')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Train an SVM with a linear kernel\n", "clf = SVC(kernel='linear')\n", "clf.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 7, "id": "ec23a526", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.98      0.93      0.95        54\n", "           1       0.96      0.99      0.97        89\n", "\n", "    accuracy                           0.97       143\n", "   macro avg       0.97      0.96      0.96       143\n", "weighted avg       0.97      0.97      0.96       143\n", "\n", "Confusion Matrix:\n", " [[50  4]\n", " [ 1 88]]\n"]}], "source": ["# Predictions and evaluation\n", "y_pred = clf.predict(X_test)\n", "print(\"Classification Report:\\n\", classification_report(y_test, y_pred))\n", "print(\"Confusion Matrix:\\n\", confusion_matrix(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": null, "id": "1222a286", "metadata": {}, "outputs": [], "source": ["# Plot decision boundary\n", "h = 0.02\n", "x_min, x_max = X_train[:, 0].min() - 1, X_train[:, 0].max() + 1\n", "y_min, y_max = X_train[:, 1].min() - 1, X_train[:, 1].max() + 1\n", "xx, yy = np.meshgrid(np.arange(x_min, x_max, h),\n", "                     np.arange(y_min, y_max, h))\n", "Z = clf.predict(np.c_[xx.ravel(), yy.ravel()])\n", "Z = Z.reshape(xx.shape)\n", "\n", "plt.figure(figsize=(8, 6))\n", "plt.contourf(xx, yy, Z, alpha=0.3, cmap='bwr')\n", "plt.scatter(X_train[:, 0], X_train[:, 1], c=y_train, edgecolors='k', label='Train')\n", "plt.scatter(X_test[:, 0], X_test[:, 1], c=y_test, marker='^', edgecolors='k', label='Test')\n", "plt.title(\"SVM Decision Boundary (Breast Cancer - PCA)\")\n", "plt.xlabel(\"PCA Component 1\")\n", "plt.ylabel(\"PCA Component 2\")\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}