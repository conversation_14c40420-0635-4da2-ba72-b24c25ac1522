{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c1cd277e-1caf-4be0-a05d-4580e8e7c42e", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named '<PERSON><PERSON><PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mC<PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplt\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msklearn\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m datasets\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mf<PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01ms<PERSON>arn\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodel_selection\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m train_test_split\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'matplotlib'"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn import datasets\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import classification_report, confusion_matrix"]}, {"cell_type": "code", "execution_count": null, "id": "ec5560d9-e3ac-491a-8a9a-ce4559c8a5d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of X: (569, 2)\n", "Shape of y: (569,)\n", "Feature names: ['mean radius' 'mean perimeter']\n"]}], "source": ["cancer = datasets.load_breast_cancer()\n", "X = cancer.data[:, [0, 2]]  # Mean radius (index 0) and mean perimeter (index 2)\n", "y = cancer.target\n", "\n", "# Inspect dataset\n", "print(\"Shape of X:\", X.shape)\n", "print(\"Shape of y:\", y.shape)\n", "print(\"Feature names:\", cancer.feature_names[[0, 2]])"]}, {"cell_type": "code", "execution_count": null, "id": "e5f21047-1602-4e1c-b294-1a2adfd0380c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 5 rows of feature data (X):\n", "[[ 17.99 122.8 ]\n", " [ 20.57 132.9 ]\n", " [ 19.69 130.  ]\n", " [ 11.42  77.58]\n", " [ 20.29 135.1 ]]\n", "\n", "First 5 rows of target labels (y):\n", "[0 0 0 0 0]\n", "\n", "last 5 rows of feature data (X):\n", "[[ 21.56 142.  ]\n", " [ 20.13 131.2 ]\n", " [ 16.6  108.3 ]\n", " [ 20.6  140.1 ]\n", " [  7.76  47.92]]\n", "\n", "last 5 rows of target labels (y):\n", "[0 0 0 0 1]\n"]}], "source": ["# Show the first 5 rows of X and y\n", "print(\"First 5 rows of feature data (X):\")\n", "print(X[:5])\n", "\n", "print(\"\\nFirst 5 rows of target labels (y):\")\n", "print(y[:5])\n", "# Show the first 5 rows of X and y\n", "print(\"\\nlast 5 rows of feature data (X):\")\n", "print(X[-5:])\n", "\n", "print(\"\\nlast 5 rows of target labels (y):\")\n", "print(y[-5:])"]}, {"cell_type": "code", "execution_count": null, "id": "2c1efcb8-b001-41b5-88f2-17d4e5d6079e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(X[:, 0], X[:, 1], c=y, cmap='coolwarm', edgecolors='k', marker='o')\n", "plt.title(\"Breast Cancer Dataset (Mean Radius vs Mean Perimeter)\")\n", "plt.xlabel(\"Mean Radius\")\n", "plt.ylabel(\"Mean Perimeter\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "79f86f21-eef0-4f3e-97e0-0639a379471f", "metadata": {}, "outputs": [], "source": ["# Split the dataset into training and test sets (70% train, 30% test)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=42)"]}, {"cell_type": "code", "execution_count": null, "id": "bdd92d6f-17dd-423c-8127-8b4c8bcd28fa", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>SVC(C=1, kernel=&#x27;linear&#x27;)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;SVC<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.svm.SVC.html\">?<span>Documentation for SVC</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>SVC(C=1, kernel=&#x27;linear&#x27;)</pre></div> </div></div></div></div>"], "text/plain": ["SVC(C=1, kernel='linear')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create an SVM classifier with a linear kernel\n", "svm_classifier = SVC(kernel='linear', C=1)\n", "\n", "# Train the SVM model\n", "svm_classifier.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": null, "id": "c80249ca-6917-454d-8118-48937212f1ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.94      0.85      0.89        54\n", "           1       0.91      0.97      0.94        89\n", "\n", "    accuracy                           0.92       143\n", "   macro avg       0.93      0.91      0.92       143\n", "weighted avg       0.92      0.92      0.92       143\n", "\n", "Confusion Matrix:\n", " [[46  8]\n", " [ 3 86]]\n"]}], "source": ["# Predict on the test data\n", "y_pred = svm_classifier.predict(X_test)\n", "\n", "# Classification report and confusion matrix\n", "print(\"Classification Report:\\n\", classification_report(y_test, y_pred))\n", "print(\"Confusion Matrix:\\n\", confusion_matrix(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": null, "id": "881b3bc8-a3c4-42d3-a66e-e484820a09c6", "metadata": {}, "outputs": [], "source": ["# Plotting the decision boundary (for 2D features)\n", "h = .02  # Step size in the mesh grid, reducing this gives finer resolution\n", "x_min, x_max = X_train[:, 0].min() - 0.5, X_train[:, 0].max() + 0.5  # Slightly reduce the range for zoom effect\n", "y_min, y_max = X_train[:, 1].min() - 0.5, X_train[:, 1].max() + 0.5  # Slightly reduce the range for zoom effect\n", "xx, yy = np.meshgrid(np.arange(x_min, x_max, h), np.arange(y_min, y_max, h))\n", "\n", "# Predict on the grid to plot decision boundary\n", "Z = svm_classifier.predict(np.c_[xx.ravel(), yy.ravel()])\n", "Z = Z.reshape(xx.shape)\n", "\n", "# Plot decision boundary and the training data\n", "plt.contourf(xx, yy, Z, alpha=0.8)  # Color map for the decision regions\n", "plt.scatter(X_train[:, 0], X_train[:, 1], c=y_train, marker='o', edgecolors='k', label='Train Data')\n", "plt.scatter(X_test[:, 0], X_test[:, 1], c=y_test, marker='^', edgecolors='k', label='Test Data')\n", "plt.title(\"SVM Decision Boundary (Sepal Length vs Petal Length)\")\n", "plt.xlabel(\"Mean Radius\")\n", "plt.ylabel(\"Mean Perimeter\")\n", "plt.legend()\n", "\n", "# Zooming effect by adjusting axis limits\n", "plt.xlim(x_min, x_max)\n", "plt.ylim(y_min, y_max)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "3fa4e047-2c0a-4525-9bb1-0e0154a47f93", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}