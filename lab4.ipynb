{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f9a84e1f", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "# Sigmoid activation function\n", "def sigmoid(x):\n", "    return 1 / (1 + np.exp(-x))\n", "\n", "# Derivative of sigmoid for backpropagation\n", "def sigmoid_derivative(x):\n", "    return x * (1 - x)\n", "\n", "# Input dataset (2 inputs)\n", "X = np.array([[0, 0], [0, 1], [1, 0], [1, 1]])  # OR problem\n", "\n", "# Target output (1 output)\n", "y = np.array([[0], [1], [1], [1]])\n"]}, {"cell_type": "code", "execution_count": 2, "id": "ed966d08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initial Input to Hidden Weights:\n", "[[ 0.19646919 -0.21386067 -0.27314855]\n", " [ 0.05131477  0.21946897 -0.07689354]]\n", "\n", "Initial Hidden to Output Weights:\n", "[[ 0.4807642 ]\n", " [ 0.18482974]\n", " [-0.0190681 ]]\n", "\n", "Initial Hidden Layer Biases:\n", "[[-0.06472949 -0.09409319  0.13742982]]\n", "\n", "Initial output Layer Biases:\n", "[[-0.03685665]]\n"]}], "source": ["# Initialize weights and biases randomly\n", "np.random.seed(123)\n", "input_weights = np.random.uniform(-0.5, 0.5, size=(2, 3))  # Weights from input to hidden layer\n", "hidden_weights = np.random.uniform(-0.5, 0.5, size=(3, 1))  # Weights from hidden to output layer\n", "hidden_bias = np.random.uniform(-0.3, 0.3, size=(1, 3))  # Bias for hidden layer\n", "output_bias = np.random.uniform(-0.3, 0.3, size=(1, 1))  # Bias for output layer\n", "\n", "# Learning rate\n", "lr = 0.8\n", "print(\"Initial Input to Hidden Weights:\")\n", "print(input_weights)\n", "print(\"\\nInitial Hidden to Output Weights:\")\n", "print(hidden_weights)\n", "print(\"\\nInitial Hidden Layer Biases:\")\n", "print(hidden_bias)\n", "print(\"\\nInitial output Layer Biases:\")\n", "print(output_bias)"]}, {"cell_type": "code", "execution_count": 3, "id": "cd8a5a99", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final Input to Hidden Weights:\n", "[[ 4.20503251  2.05741517 -2.75840487]\n", " [ 4.16428853  2.28660895 -2.65427959]]\n", "\n", "Final Hidden to Output Weights:\n", "[[ 6.97167202]\n", " [ 2.90569351]\n", " [-4.90359507]]\n", "\n", "Final Hidden Layer Biases:\n", "[[-2.07715528 -1.05222821  1.33015771]]\n", "\n", "Final Output Layer Bias:\n", "[[-2.30691755]]\n"]}], "source": ["# Training the neural network\n", "for epoch in range(10000):  # Train for 10,000 epochs\n", "    # Forward pass\n", "    hidden_layer_input = np.dot(X, input_weights) + hidden_bias  # Input to hidden layer\n", "    hidden_layer_output = sigmoid(hidden_layer_input)  # Output from hidden layer\n", "    output_layer_input = np.dot(hidden_layer_output, hidden_weights) + output_bias  # Input to output layer\n", "    output = sigmoid(output_layer_input)  # Final output\n", "\n", "    # Calculate error\n", "    error = y - output\n", "\n", "    # Backpropagation\n", "    d_output = error * sigmoid_derivative(output)  # Gradient for output layer\n", "    error_hidden_layer = d_output.dot(hidden_weights.T)  # Error for hidden layer\n", "    d_hidden_layer = error_hidden_layer * sigmoid_derivative(hidden_layer_output)  # Gradient for hidden layer\n", "\n", "    # Update weights and biases\n", "    hidden_weights += hidden_layer_output.T.dot(d_output) * lr  # Update weights from hidden to output\n", "    output_bias += np.sum(d_output, axis=0, keepdims=True) * lr  # Update output layer bias\n", "    input_weights += X.T.dot(d_hidden_layer) * lr  # Update weights from input to hidden\n", "    hidden_bias += np.sum(d_hidden_layer, axis=0, keepdims=True) * lr  # Update hidden layer bias\n", "\n", "# Display final weights, biases, and output\n", "print(\"Final Input to Hidden Weights:\")\n", "print(input_weights)\n", "print(\"\\nFinal Hidden to Output Weights:\")\n", "print(hidden_weights)\n", "print(\"\\nFinal Hidden Layer Biases:\")\n", "print(hidden_bias)\n", "print(\"\\nFinal Output Layer Bias:\")\n", "print(output_bias)"]}, {"cell_type": "code", "execution_count": 4, "id": "c9337efa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Final Output after Training:\n", "[[0.00940784]\n", " [0.9940337 ]\n", " [0.99395085]\n", " [0.99937179]]\n"]}], "source": ["print(\"\\nFinal Output after Training:\")\n", "print(output)"]}, {"cell_type": "code", "execution_count": 5, "id": "6cdbc988", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test Input: [1, 0]\n", "Output after training: [[0.99395119]]\n"]}], "source": ["# Test input\n", "test_input = np.array([1, 0])  # Input to test the network\n", "\n", "# Forward pass with updated weights and biases\n", "hidden_layer_input = np.dot(test_input, input_weights) + hidden_bias  # Input to hidden layer\n", "hidden_layer_output = sigmoid(hidden_layer_input)  # Output from hidden layer\n", "output_layer_input = np.dot(hidden_layer_output, hidden_weights) + output_bias  # Input to output layer\n", "test_output = sigmoid(output_layer_input)  # Final output\n", "\n", "# Display the output\n", "print(\"Test Input: [1, 0]\")\n", "print(\"Output after training:\", test_output)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}