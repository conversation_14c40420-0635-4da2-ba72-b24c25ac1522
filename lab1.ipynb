{"cells": [{"cell_type": "code", "execution_count": null, "id": "5351b1cb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.impute import SimpleImputer\n", "\n", "# Step 1: Original Dataset\n", "data = {\n", "    \"Name\": [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\"],\n", "    \"Age\": [25, 101, np.nan, 35, 40, 25],\n", "    \"Salary\": [50000, 54000, np.nan, 60000, 70000, 50000],\n", "    \"Department\": [\"hr\", \"Engineering\", \"HR\", np.nan, \"Marketing\", \"HR\"],\n", "    \"Joined\": [\"2015/06/01\", \"2018-07-15\", \"2020-01-12\", \"2012-03-09\", \"2019/11/23\", \"2015/06/01\"],\n", "}\n", "df = pd.DataFrame(data)\n", "print(df)\n"]}, {"cell_type": "code", "execution_count": null, "id": "5ddf27c6", "metadata": {}, "outputs": [], "source": ["# Step 2: Simulated Redundant New Data with Inconsistencies\n", "new_data = pd.DataFrame({\n", "    \"Name\": [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\"],\n", "    \"Age\": [28, 25, 40],\n", "    \"Salary\": [55000, 50000, 70000],\n", "    \"Department\": [\"engineering\", \"HR\", \"marketing\"],\n", "    \"Joined\": [\"2021-05-14\", \"2015-06-01\", \"2019-11-23\"],\n", "})\n", "\n", "print(new_data)\n"]}, {"cell_type": "code", "execution_count": null, "id": "4053c4c8", "metadata": {}, "outputs": [], "source": ["# Step 3: Integration\n", "df = pd.concat([df, new_data], ignore_index=True)\n", "print(df)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9cc59b2e", "metadata": {}, "outputs": [], "source": ["# Replace invalid or out-of-range values in numerical columns\n", "df[\"Age\"] = df[\"Age\"].apply(lambda x: x if pd.isnull(x) or (0 <= x <= 60) else np.nan)\n", "\n", "print(df)\n"]}, {"cell_type": "code", "execution_count": null, "id": "52d801ea", "metadata": {}, "outputs": [], "source": ["# Step 4: Data Cleaning Handle missing values\n", "imputer = SimpleImputer(strategy='most_frequent')\n", "df[['Name', 'Age', 'Salary', 'Department']] = imputer.fit_transform(df[['Name', 'Age', 'Salary', 'Department']])\n", "print(df)\n"]}, {"cell_type": "code", "execution_count": null, "id": "42255dc7", "metadata": {}, "outputs": [], "source": ["# Step 5: Handle inconsistencies\n", "df['Department'] = df['Department'].str.strip().str.title()  # Standardize department names\n", "df['Joined'] = pd.to_datetime(df['Joined'], errors='coerce')  # Convert to datetime\n", "df['Age'] = pd.to_numeric(df['Age'], errors='coerce')\n", "df['Salary'] = pd.to_numeric(df['Salary'], errors='coerce')\n", "\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "id": "3fd30710", "metadata": {}, "outputs": [], "source": ["# Step 6: Remove duplicates\n", "df.drop_duplicates(subset=['Name', 'Age', 'Salary', 'Department', 'Joined'], inplace=True)\n", "\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "id": "6ac9998e", "metadata": {}, "outputs": [], "source": ["# Step 7: Binning on <PERSON><PERSON>\n", "salary_bins = [0, 52000, 60000, 80000]\n", "salary_labels = ['Low', 'Medium', 'High']\n", "df['Salary_Binned'] = pd.cut(df['Salary'], bins=salary_bins, labels=salary_labels, include_lowest=True)\n", "\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "id": "cf2d67eb", "metadata": {}, "outputs": [], "source": ["# Step 8: Bin Mean Calculation\n", "bin_means = df.groupby('Sal<PERSON>_Binned')['Salary'].mean()"]}, {"cell_type": "code", "execution_count": null, "id": "ee7e6717", "metadata": {}, "outputs": [], "source": ["# Final Output\n", "print(\"Final Dataset:\")\n", "print(df[['Name', 'Sal<PERSON>', 'Department', 'Joined', '<PERSON><PERSON>_Binned']])\n", "\n", "print(\"\\nMean Salary per Bin:\")\n", "print(bin_means)"]}, {"cell_type": "code", "execution_count": null, "id": "c1cb2b3d", "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "\n", "# Step 9: Normalize Numerical Features\n", "scaler = MinMaxScaler()\n", "df[[\"Age\", \"Salary\"]] = scaler.fit_transform(df[[\"Age\", \"Salary\"]])\n", "\n", "print(\"\\nAfter normalizing numerical features:\")\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "id": "4754c9ae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}